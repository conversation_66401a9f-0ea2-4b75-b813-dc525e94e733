// src\ConfigModule.hpp
#include <Arduino.h>

#include "ErrorDefinitions.h"
#include "StatusDefinitions.h"

#include "AllModule/RGB/RGBConfig.hpp"
#include "AllModule/Button/ButtonConfig.hpp"
#include "AllModule/SD/SDConfig.hpp"

#include "AllNode/LoRaWAN/LoRaWANConfig.hpp"
#include "AllNode/RS485/RS485Config.hpp"
#include "AllNode/RS485/LinkSensorConfig.hpp"
#include "AllNode/RS485/RoutineSensorConfig.hpp"
#include "AllNode/LoRa/LoRaMessageConfig.hpp"
#include "AllNode/Sleep/SleepConfig.hpp"

#include "AllModule/WiFi/WiFiConfig.hpp"
#include "AllModule/WebServer/WebServerConfig.hpp"
#include "AllModule/WebSockets/WebSocketsConfig.hpp"
#include "AllModule/Updates/UpdatesConfig.hpp"

bool localWebEnabled = false;

#ifdef ENV_WROOM
#define ACTIVATION_PIN 27
#elif defined(ENV_TTGO)
#define ACTIVATION_PIN 12
#endif