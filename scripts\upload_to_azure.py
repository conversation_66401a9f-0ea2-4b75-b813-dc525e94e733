import os
import sys
import json
from azure.storage.blob import BlobServiceClient

# Obtener variables de entorno
connect_str = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
account_name = os.getenv('AZURE_STORAGE_ACCOUNT_NAME')

if not connect_str or not account_name:
    raise ValueError("AZURE_STORAGE_CONNECTION_STRING or AZURE_STORAGE_ACCOUNT_NAME is not set or is empty")

container_name = 'firmware'

# Nombre del archivo a subir
file_path = sys.argv[1]
blob_name = os.path.basename(file_path)

# Crear el cliente de BlobService
blob_service_client = BlobServiceClient.from_connection_string(connect_str)

# Crear un cliente de contenedor
container_client = blob_service_client.get_container_client(container_name)

# Subir el archivo
with open(file_path, "rb") as data:
    container_client.upload_blob(name=blob_name, data=data, overwrite=True)

print(f"Archivo {blob_name} subido a {container_name} con éxito.")

# Crear/actualizar el archivo JSON
json_path = 'versions.json'
blob_url = f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}"

# Determinar el entorno y la versión del archivo subido
if "prod" in blob_name:
    environment = "production"
    version = blob_name.split('_')[1]
    entry = {"version": version, "url": blob_url}
elif "preprod" in blob_name:
    environment = "preproduction"
    version = blob_name.split('_')[1]
    entry = {"version": version, "url": blob_url}
else:  # Assuming it is development
    environment = "development"
    version = blob_name.split('_')[1]
    commit = blob_name.split('_')[2].split('.')[0]
    entry = {"version": version, "commit": commit, "url": blob_url}

# Descargar el archivo JSON existente desde el blob storage
try:
    download_blob = container_client.download_blob('versions.json')
    versions_data = download_blob.readall()
    data = json.loads(versions_data)
except Exception as e:
    print(f"No existing versions.json found. A new one will be created. {e}")
    data = {"production": [], "preproduction": [], "development": []}

# Actualizar el JSON con la nueva entrada
data[environment].append(entry)

# Guardar el JSON actualizado localmente
with open(json_path, 'w') as file:
    json.dump(data, file, indent=4)

# Subir el JSON actualizado al blob storage
with open(json_path, "rb") as data:
    container_client.upload_blob(name='versions.json', data=data, overwrite=True)

print(f"Archivo versions.json actualizado y subido con éxito.")
