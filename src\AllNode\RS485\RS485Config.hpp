// src\AllNode\RS485\RS485Config.hpp
#ifdef ENV_WROOM
#include <HardwareSerial.h>
#elif defined(ENV_TTGO)
#include <SoftwareSerial.h>
#endif

#include <ArduinoJson.h>
#include <vector>

#ifdef ENV_WROOM
#define RX_RS485_PIN 16
#define TX_RS485_PIN 17
#elif defined(ENV_TTGO)
#define RX_RS485_PIN 21
#define TX_RS485_PIN 22
#endif

#define BAUD_RS485 9600

#ifdef ENV_WROOM
HardwareSerial rs485Serial(2);
#elif defined(ENV_TTGO)
SoftwareSerial rs485Serial(RX_RS485_PIN, TX_RS485_PIN);
#endif