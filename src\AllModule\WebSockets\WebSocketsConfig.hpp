// src\AllModule\WebSockets\WebSocketsConfig.hpp
#ifndef WEBSOCKETSCONFIG_HPP
#define WEBSOCKETSCONFIG_HPP

AsyncWebSocket ws("/ws");

#define CARDS_AVAILABLE_PATH "/cards.json"
#define SYSTEM_INFO_PATH "/system_info.json"

void sendSensorUpdates();

Task tSendSensorUpdates(TASK_SECOND * 15, TASK_FOREVER, &sendSensorUpdates, &runner);

int numClients = 0;
bool webSocketEnabled = false;

#endif // WEBSOCKETSCONFIG_HPP
