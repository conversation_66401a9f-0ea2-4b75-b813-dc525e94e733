// src\AllModule\WebSockets\SystemEventFunctions.hpp
#ifndef SYSTEMEVENTFUNCTIONS_HPP
#define SYSTEMEVENTFUNCTIONS_HPP

void handleGetSystemInfo(AsyncWebSocketClient *client)
{
    File file = SD.open(SYSTEM_INFO_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open system info file.");
        client->text("{\"type\":\"system_info\",\"model\":\"unknown\",\"version\":\"unknown\",\"deviceId\":\"unknown\"}");
        return;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse system info file.");
        client->text("{\"type\":\"system_info\",\"model\":\"unknown\",\"version\":\"unknown\",\"deviceId\":\"unknown\"}");
        return;
    }

    JsonDocument responseDoc;
    responseDoc["type"] = "system_info";
    responseDoc["model"] = doc["model"].as<String>();
    responseDoc["version"] = doc["version"].as<String>();
    responseDoc["deviceId"] = doc["deviceId"].as<String>();

    String jsonResponse;
    serializeJson(responseDoc, jsonResponse);
    client->text(jsonResponse);
}

String generateUUID()
{
    uint64_t chipid = ESP.getEfuseMac();

    char formattedChipID[14];
    sprintf(formattedChipID, "N%012llX", (unsigned long long)chipid);

    return String(formattedChipID);
}

void handleRegenerateSystemInfo(AsyncWebSocketClient *client)
{
    // Crear un nuevo ID único usando la función getUUID()
    String newDeviceId = generateUUID();

    // Leer el archivo de configuración actual
    File file = SD.open(SYSTEM_INFO_PATH, FILE_READ);
    JsonDocument doc;
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (error)
        {
            Serial.println("Failed to parse system info file");
            client->text("{\"type\":\"regenerated_system_info\",\"status\":\"failed\"}");
            return;
        }
    }
    else
    {
        Serial.println("Failed to open system info file");
        client->text("{\"type\":\"regenerated_system_info\",\"status\":\"failed\"}");
        return;
    }

    // Actualizar los valores del JSON
    doc["deviceId"] = newDeviceId;
    doc["version"] = String(latestBuildTag);

    // Guardar el JSON actualizado en la SD
    file = SD.open(SYSTEM_INFO_PATH, FILE_WRITE);
    if (file)
    {
        serializeJson(doc, file);
        file.close();
    }
    else
    {
        Serial.println("Failed to write to system info file");
        client->text("{\"type\":\"regenerated_system_info\",\"status\":\"failed\"}");
        return;
    }

    // Responder al cliente con los nuevos valores
    JsonDocument responseDoc;
    responseDoc["type"] = "regenerated_system_info";
    responseDoc["model"] = doc["model"];
    responseDoc["version"] = doc["version"];
    responseDoc["deviceId"] = doc["deviceId"];

    String response;
    serializeJson(responseDoc, response);
    client->text(response);
}

#endif // SYSTEMEVENTFUNCTIONS_HPP