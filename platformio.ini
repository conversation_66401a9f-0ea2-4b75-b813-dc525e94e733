; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
board_build.partitions = min_spiffs.csv

extra_scripts = pre:scripts/generate_buildinfo.py

build_flags = 
    -D hal_init=LMICHAL_init
    -D ENV_WROOM
    -D SLEEP_MODE

lib_deps =
    adafruit/Adafruit NeoPixel@^1.12.2
    sandeepmistry/LoRa@^0.8.0
    plerup/EspSoftwareSerial@^8.2.0
    bblanchon/Arduino<PERSON>son@^7.1.0
    arkhipenko/TaskScheduler@^3.7.0
    https://github.com/KeithHanson/ESPAsyncWebServer.git
    mcci-catena/MCCI LoRaWAN LMIC library@^4.1.1

[env:ttgo-lora32-v21]
platform = espressif32
board = ttgo-lora32-v21
framework = arduino
monitor_speed = 115200
board_build.partitions = min_spiffs.csv

extra_scripts = pre:scripts/generate_buildinfo.py

build_flags = 
    -D hal_init=LMICHAL_init
    -D ENV_TTGO
    ; -D SLEEP_MODE

lib_deps =
    adafruit/Adafruit NeoPixel@^1.12.2
    sandeepmistry/LoRa@^0.8.0
    plerup/EspSoftwareSerial@^8.2.0
    bblanchon/ArduinoJson@^7.1.0
    arkhipenko/TaskScheduler@^3.7.0
    https://github.com/KeithHanson/ESPAsyncWebServer.git
    mcci-catena/MCCI LoRaWAN LMIC library@^4.1.1
