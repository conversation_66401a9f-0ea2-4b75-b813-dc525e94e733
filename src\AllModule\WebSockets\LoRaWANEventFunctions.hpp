// src\AllModule\WebSockets\LoRaWANEventFunctions.hpp
#ifndef LORAWANEVENTFUNCTIONS_HPP
#define LORAWANEVENTFUNCTIONS_HPP

String convertToHexString(const JsonArray &array, bool invert = false)
{
    String hexString = "";
    if (invert)
    {
        for (int i = array.size() - 1; i >= 0; i--)
        {
            uint8_t value = array[i].as<uint8_t>(); // Convertir explícitamente a uint8_t
            if (value < 0x10)
            {
                hexString += "0"; // Añadir un cero para valores de un solo dígito
            }
            hexString += String(value, HEX);
        }
    }
    else
    {
        for (int i = 0; i < array.size(); i++)
        {
            uint8_t value = array[i].as<uint8_t>(); // Convertir explícitamente a uint8_t
            if (value < 0x10)
            {
                hexString += "0"; // Añadir un cero para valores de un solo dígito
            }
            hexString += String(value, HEX);
        }
    }
    return hexString;
}

void handleGetLorawanCredentials(AsyncWebSocketClient *client)
{
    File file = SD.open(LORAWAN_CREDENTIALS_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open LoRaWAN credentials file");
        client->text("{\"type\":\"error\", \"message\":\"Failed to open credentials file\"}");
        return;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse LoRaWAN credentials file");
        client->text("{\"type\":\"error\", \"message\":\"Failed to parse credentials file\"}");
        return;
    }

    // Obtener los arrays de APPEUI, DEVEUI, y APPKEY
    JsonArray appeuiArray = doc["APPEUI"].as<JsonArray>();
    JsonArray deveuiArray = doc["DEVEUI"].as<JsonArray>();
    JsonArray appkeyArray = doc["APPKEY"].as<JsonArray>();

    // Convertir los arrays a hexadecimal
    String appeui = convertToHexString(appeuiArray, true); // Invertir para APPEUI y DEVEUI
    String deveui = convertToHexString(deveuiArray, true);
    String appkey = convertToHexString(appkeyArray);

    // Crear la respuesta JSON
    JsonDocument responseDoc;
    responseDoc["type"] = "lorawan_credentials";
    responseDoc["appeui"] = appeui;
    responseDoc["deveui"] = deveui;
    responseDoc["appkey"] = appkey;

    String jsonResponse;
    serializeJson(responseDoc, jsonResponse);
    client->text(jsonResponse);

    Serial.println("Sent LoRaWAN credentials:");
    Serial.println(jsonResponse);
}

void handleGetLorawanSession(AsyncWebSocketClient *client)
{
    File file = SD.open(LORAWAN_SESSION_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open lorawan_session.json");
        client->text("{\"type\":\"error\", \"message\":\"Failed to open lorawan_session.json\"}");
        return;
    }

    // Leer el archivo JSON
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();
    if (error)
    {
        Serial.println("Failed to parse lorawan_session.json");
        client->text("{\"type\":\"error\", \"message\":\"Failed to parse lorawan_session.json\"}");
        return;
    }

    // Extraer los datos de sesión
    uint32_t netid = doc["netid"];
    uint32_t devaddr = doc["devaddr"];
    JsonArray nwkKeyArray = doc["nwkKey"];
    JsonArray artKeyArray = doc["artKey"];
    uint32_t fCntUp = doc["fCntUp"];

    // Convertir los arrays a cadenas hexadecimales
    String nwkKey = convertToHexString(nwkKeyArray, false); // No necesita inversión de bytes
    String artKey = convertToHexString(artKeyArray, false); // No necesita inversión de bytes

    // Preparar la respuesta
    JsonDocument responseDoc;
    responseDoc["type"] = "lorawan_session";
    responseDoc["netid"] = String(netid, DEC);     // Devolver netid como decimal
    responseDoc["devaddr"] = String(devaddr, HEX); // Convertir devaddr a hexadecimal
    responseDoc["nwkKey"] = nwkKey;                // Clave en formato hexadecimal
    responseDoc["artKey"] = artKey;                // Clave en formato hexadecimal
    responseDoc["fCntUp"] = fCntUp;                // Devolver fCntUp como decimal

    // Enviar la respuesta al cliente
    String jsonResponse;
    serializeJson(responseDoc, jsonResponse);
    Serial.println(jsonResponse);
    client->text(jsonResponse);
}

bool convertHexToByteArray(const String &hexString, u1_t *byteArray, size_t arraySize, bool reverseBytes)
{
    if (hexString.length() != arraySize * 2)
    {
        Serial.println("Error: Hex string length does not match expected byte array size.");
        return false;
    }

    for (size_t i = 0; i < arraySize; i++)
    {
        String byteStr = hexString.substring(i * 2, i * 2 + 2);
        byteArray[i] = strtoul(byteStr.c_str(), nullptr, 16);
        Serial.print("Converted byte: ");
        Serial.println(byteArray[i], HEX);
    }

    if (reverseBytes)
    {
        std::reverse(byteArray, byteArray + arraySize);
        Serial.println("Reversed byte array:");
        for (size_t i = 0; i < arraySize; i++)
        {
            Serial.print(byteArray[i], HEX);
            Serial.print(" ");
        }
        Serial.println();
    }

    return true;
}

bool saveLorawanCredentials(const u1_t *appeui, const u1_t *deveui, const u1_t *appkey)
{
    // Crear un documento JSON para almacenar las credenciales
    JsonDocument doc; // Ajustar el tamaño según lo necesario

    JsonArray appeuiJsonArray = doc["APPEUI"].to<JsonArray>();
    JsonArray deveuiJsonArray = doc["DEVEUI"].to<JsonArray>();
    JsonArray appkeyJsonArray = doc["APPKEY"].to<JsonArray>();

    // Añadir los arrays de bytes al JSON
    for (int i = 0; i < 8; i++)
    {
        appeuiJsonArray.add(appeui[i]);
        deveuiJsonArray.add(deveui[i]);
    }

    for (int i = 0; i < 16; i++)
    {
        appkeyJsonArray.add(appkey[i]);
    }

    // Abrir el archivo de credenciales LoRaWAN en la SD para escribir
    File file = SD.open(LORAWAN_CREDENTIALS_PATH, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open lorawan_credentials.json for writing");
        return false;
    }

    // Escribir el JSON en el archivo
    if (serializeJson(doc, file) == 0)
    {
        Serial.println("Failed to write to lorawan_credentials.json");
        file.close();
        return false;
    }

    file.close();
    Serial.println("LoRaWAN credentials saved successfully.");
    return true;
}

void handleUpdateLorawanCredentials(AsyncWebSocketClient *client, String appeui, String deveui, String appkey)
{
    Serial.println("=== Start updating LoRaWAN Credentials ===");

    // Mostrar los valores recibidos
    Serial.println("Received APPEUI: " + appeui);
    Serial.println("Received DEVEUI: " + deveui);
    Serial.println("Received APPKEY: " + appkey);

    // Convertir los valores hexadecimal a array de bytes
    u1_t appeuiArray[8];
    u1_t deveuiArray[8];
    u1_t appkeyArray[16];

    if (!convertHexToByteArray(appeui, appeuiArray, sizeof(appeuiArray), true) ||
        !convertHexToByteArray(deveui, deveuiArray, sizeof(deveuiArray), true) ||
        !convertHexToByteArray(appkey, appkeyArray, sizeof(appkeyArray), false))
    {
        Serial.println("Error: Failed to convert hex string to byte array.");
        client->text("{\"type\":\"error\", \"message\":\"Failed to convert hex string to byte array.\"}");
        return;
    }

    // Guardar las credenciales en el archivo JSON
    if (!saveLorawanCredentials(appeuiArray, deveuiArray, appkeyArray))
    {
        client->text("{\"type\":\"error\", \"message\":\"Failed to save LoRaWAN credentials.\"}");
    }
    else
    {
        client->text("{\"type\":\"update_lorawan_credentials_success\"}");
    }
}

bool saveLorawanSession(u4_t netid, u4_t devaddr, const u1_t *nwkKey, const u1_t *artKey, int fCntUp)
{
    // Crear un documento JSON para almacenar la sesión
    JsonDocument doc;  // Ajustar el tamaño según sea necesario

    doc["netid"] = netid;
    doc["devaddr"] = devaddr;

    JsonArray nwkKeyJsonArray = doc["nwkKey"].to<JsonArray>();
    JsonArray artKeyJsonArray = doc["artKey"].to<JsonArray>();

    // Añadir las llaves de red y aplicación al JSON
    for (int i = 0; i < 16; i++)
    {
        nwkKeyJsonArray.add(nwkKey[i]);
        artKeyJsonArray.add(artKey[i]);
    }

    doc["fCntUp"] = fCntUp;

    // Abrir el archivo en la SD para escribir la sesión
    File file = SD.open(LORAWAN_SESSION_PATH, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open lorawan_session.json for writing");
        return false;
    }

    // Escribir el JSON en el archivo
    if (serializeJson(doc, file) == 0)
    {
        Serial.println("Failed to write to lorawan_session.json");
        file.close();
        return false;
    }

    file.close();
    Serial.println("LoRaWAN session saved successfully.");
    return true;
}

void handleUpdateLorawanSession(AsyncWebSocketClient *client, String netid, String devaddr, String nwkKey, String artKey, int fCntUp)
{
    Serial.println("=== Start updating LoRaWAN Session ===");

    // Mostrar los valores recibidos
    Serial.println("Received NETID: " + netid);
    Serial.println("Received DEVADDR: " + devaddr);
    Serial.println("Received NWKKEY: " + nwkKey);
    Serial.println("Received ARTKEY: " + artKey);
    Serial.println("Received FCNTUP: " + String(fCntUp));

    // Convertir los valores hexadecimal a array de bytes
    u4_t netidValue = strtoul(netid.c_str(), nullptr, 16);
    u4_t devaddrValue = strtoul(devaddr.c_str(), nullptr, 16);
    u1_t nwkKeyArray[16];
    u1_t artKeyArray[16];

    if (!convertHexToByteArray(nwkKey, nwkKeyArray, sizeof(nwkKeyArray), false) ||
        !convertHexToByteArray(artKey, artKeyArray, sizeof(artKeyArray), false))
    {
        Serial.println("Error: Failed to convert hex string to byte array.");
        client->text("{\"type\":\"error\", \"message\":\"Failed to convert hex string to byte array.\"}");
        return;
    }

    // Guardar la sesión en el archivo JSON
    if (!saveLorawanSession(netidValue, devaddrValue, nwkKeyArray, artKeyArray, fCntUp))
    {
        client->text("{\"type\":\"error\", \"message\":\"Failed to save LoRaWAN session.\"}");
    }
    else
    {
        client->text("{\"type\":\"update_lorawan_session_success\"}");
    }
}

#endif // LORAWANEVENTFUNCTIONS_HPP