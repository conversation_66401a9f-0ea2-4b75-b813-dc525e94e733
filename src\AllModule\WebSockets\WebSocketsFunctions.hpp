// src\AllModule\WebSockets\WebSocketsFunctions.hpp
#ifndef WEBSOCKETSFUNCTIONS_HPP
#define WEBSOCKETSFUNCTIONS_HPP

#include "RoutineSenEventFunctions.hpp"
#include "LinkedSenEventFunctions.hpp"
#include "CardsEventFunctions.hpp"
#include "CredentialsEventFunctions.hpp"
#include "SleepEventFunctions.hpp"
#include "SystemEventFunctions.hpp"
#include "UpdatesEventFunctions.hpp"
#include "UnlinkedSenEventFunctions.hpp"
#include "LoRaWANEventFunctions.hpp"

void onWsEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type, void *arg, uint8_t *data, size_t len)
{
    if (type == WS_EVT_CONNECT)
    {
        Serial.println("WebSocket client connected");
        numClients++;
        if (!tSendSensorUpdates.isEnabled())
        {
            tSendSensorUpdates.enable();
        }
        if (!tSensorRoutine.isEnabled())
        {
            tSensorRoutine.enable();
        }
        // if (!tSendLoRaMessages.isEnabled())
        // {
        //     tSendLoRaMessages.enable();
        // }
    }
    else if (type == WS_EVT_DISCONNECT)
    {
        Serial.println("WebSocket client disconnected");
        numClients--;
        if (numClients == 0 && tSendSensorUpdates.isEnabled())
        {
            tSendSensorUpdates.disable();
        }
        if (!tSensorRoutine.isEnabled())
        {
            tSensorRoutine.enable();
        }
        // if (!tSendLoRaMessages.isEnabled())
        // {
        //     tSendLoRaMessages.enable();
        // }
    }
    else if (type == WS_EVT_DATA)
    {
        AwsFrameInfo *info = (AwsFrameInfo *)arg;
        if (info->final && info->index == 0 && info->len == len)
        {
            if (info->opcode == WS_TEXT)
            {
                data[len] = 0;
                String msg = (char *)data;
                Serial.printf("Received: %s\n", msg.c_str());

                if (msg == "{\"type\":\"get_sensors\"}")
                {
                    handleGetSensorsRequest(client);
                }
                else if (msg == "{\"type\":\"get_cards\"}")
                {
                    handleGetCardsRequest(client);
                }
                else if (msg.startsWith("{\"type\":\"save_card\""))
                {
                    handleSaveCardRequest(client, msg);
                }
                else if (msg.startsWith("{\"type\":\"get_sensor_details\""))
                {
                    JsonDocument doc;
                    deserializeJson(doc, msg);
                    int sensorId = doc["id"];
                    handleGetSensorDetails(client, sensorId);
                }
                else if (msg.startsWith("{\"type\":\"update_offset\""))
                {
                    JsonDocument doc;
                    deserializeJson(doc, msg);
                    int sensorId = doc["id"];
                    String sign = doc["sign"];
                    float offset = doc["offset"];
                    if (sign == "-")
                    {
                        offset = -offset;
                    }
                    handleUpdateOffset(client, sensorId, offset);
                }
                else if (msg == "{\"type\":\"get_system_info\"}")
                {
                    handleGetSystemInfo(client);
                }
                else if (msg == "{\"type\":\"get_credentials\"}")
                {
                    handleGetCredentials(client);
                }
                else if (msg.startsWith("{\"type\":\"update_credentials\""))
                {
                    handleUpdateCredentials(client, msg);
                }
                else if (msg == "{\"type\":\"get_sleep_config\"}")
                {
                    handleGetSleepConfig(client);
                }
                else if (msg.startsWith("{\"type\":\"update_sleep_config\""))
                {
                    handleUpdateSleepConfig(client, msg);
                }
                else if (msg == "{\"type\":\"regenerate_system_info\"}")
                {
                    handleRegenerateSystemInfo(client);
                }
                else if (msg.startsWith("{\"type\":\"ota_update\""))
                {
                    handleOTAUpdate(client, msg);
                }
                else if (msg.startsWith("{\"type\":\"delete_card\""))
                {
                    JsonDocument doc;
                    deserializeJson(doc, msg);
                    int cardId = doc["id"];
                    handleDeleteCardRequest(client, cardId);
                }
                else if (msg.startsWith("{\"type\":\"get_rs485_id\""))
                {
                    handleGetRS485Id(client);
                }
                else if (msg.startsWith("{\"type\":\"set_rs485_id\""))
                {
                    JsonDocument doc;
                    deserializeJson(doc, msg);
                    uint8_t currentId = doc["currentId"];
                    uint8_t newId = doc["newId"];
                    handleSetRS485Id(client, currentId, newId);
                }
                else if (msg.startsWith("{\"type\":\"get_sensor_info\""))
                {
                    handleGetSensorInfo(client);
                }
                else if (msg.startsWith("{\"type\":\"unlink_sensor\""))
                {
                    JsonDocument doc;
                    deserializeJson(doc, msg);
                    uint8_t currentId = doc["id"];
                    handleUnlinkSensor(client, currentId);
                }
                else if (msg.startsWith("{\"type\":\"get_lorawan_credentials\""))
                {
                    handleGetLorawanCredentials(client);
                }
                else if (msg.startsWith("{\"type\":\"update_lorawan_credentials\""))
                {
                    JsonDocument doc;
                    deserializeJson(doc, msg);
                    String appeui = doc["appeui"];
                    String deveui = doc["deveui"];
                    String appkey = doc["appkey"];
                    Serial.println("Received APPEUI: " + appeui);
                    Serial.println("Received DEVEUI: " + deveui);
                    Serial.println("Received APPKEY: " + appkey);
                    handleUpdateLorawanCredentials(client, appeui, deveui, appkey);
                }
                else if (msg.startsWith("{\"type\":\"get_lorawan_session\""))
                {
                    handleGetLorawanSession(client);
                }
                else if (msg.startsWith("{\"type\":\"update_lorawan_session\""))
                {
                    JsonDocument doc;
                    deserializeJson(doc, msg);
                    String netid = doc["netid"];
                    String devaddr = doc["devaddr"];
                    String nwkKey = doc["nwkKey"];
                    String artKey = doc["artKey"];
                    int fCntUp = doc["fCntUp"];
                    handleUpdateLorawanSession(client, netid, devaddr, nwkKey, artKey, fCntUp);
                }
            }
        }
    }
}

void initializeWebSockets()
{
    ws.onEvent(onWsEvent);
    server.addHandler(&ws);
    Serial.println("WebSocket initialized.");
}

void loopWebSockets()
{
    ws.cleanupClients();
}

void handleWebSocket()
{
    if (webSocketEnabled)
    {
        initializeWebSockets();
        if (!tSendSensorUpdates.isEnabled())
        {
            tSendSensorUpdates.enable();
        }
    }
    else
    {
        if (tSendSensorUpdates.isEnabled())
        {
            tSendSensorUpdates.disable();
        }
        ws.closeAll();
        Serial.println("WebSocket disabled.");
    }
}

void setWebSocketEnabled(bool enabled)
{
    webSocketEnabled = enabled;
    handleWebSocket();
}

#endif // WEBSOCKETSFUNCTIONS_HPP
