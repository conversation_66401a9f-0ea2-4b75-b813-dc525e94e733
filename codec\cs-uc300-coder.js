function encodeDownlink(input) {
  var command = input.data.data.command.toLowerCase();
  var hexPayload = "";

  if (command === "output") {
    var gpioOut = input.data.data.gpioOut;
    var stateOut = input.data.data.stateOut;

    if (gpioOut === 1) {
      hexPayload = "07"; // Si gpioOut es 1, manda 07
      hexPayload += stateOut === 1 ? "01ff" : "00ff";
    } else if (gpioOut === 2) {
      hexPayload = "08"; // Si gpioOut es 2, manda 08
      hexPayload += stateOut === 1 ? "01ff" : "00ff";
    } else {
      hexPayload = "00"; // Si gpioOut no es 1 ni 2, manda 00
    }
  } else if (command === "rule") {
    var gpioOut = input.data.data.gpioOut;
    var stateOut = input.data.data.stateOut;
    var duration = input.data.data.duration;

    if (gpioOut === 1) {
      hexPayload = "ff93"; // Comando para DO1
      hexPayload += stateOut === 1 ? "0101" : "0100";
    } else if (gpioOut === 2) {
      hexPayload = "ff93"; // Comando para DO2
      hexPayload += stateOut === 1 ? "0201" : "0200";
    }

    // Convertir duration a hexadecimal (4 bytes)
    var durationHex = duration.toString(16).padStart(8, "0"); // Convertir y rellenar con ceros
    var durationReversed = durationHex
      .match(/.{1,2}/g)
      .reverse()
      .join(""); // Invertir los bytes

    // Agregar duración al final del hexPayload
    hexPayload += durationReversed;
  } else {
    hexPayload = "00"; // Si el comando NO es "output" o "rule", manda 00
  }

  // Convertir hexPayload en array de bytes
  var bytes = [];
  for (var i = 0; i < hexPayload.length; i += 2) {
    bytes.push(parseInt(hexPayload.substr(i, 2), 16));
  }

  return { bytes: bytes };
}

// 🔹 Ejemplo de uso:
// let jsonData = {
//   data: {
//     data: {
//       command: "output",
//       idController: 1,
//       gpioOut: 2,
//       stateOut: 1,
//       typeController: 1,
//     },
//   },
// };

// 🔹 Ejemplo de uso:
let jsonData = {
  data: {
    data: {
      command: "rule",
      idController: 1,
      gpioOut: 2,
      stateOut: 1,
      typeController: 1,
      duration: 60000,
    },
  },
};

let result = encodeDownlink(jsonData);
console.log("Bytes Result:", result.bytes);
