import json

def convert_to_decimal_array(value, reverse=False):
    # Dividir la cadena en pares de caracteres
    hex_pairs = [value[i:i+2] for i in range(0, len(value), 2)]
    
    # Invertir la lista si es necesario (para App y Dev)
    if reverse:
        hex_pairs = hex_pairs[::-1]
    
    # Convertir los pares hexadecimales a enteros en formato decimal
    decimal_array = [int(pair, 16) for pair in hex_pairs]
    
    return decimal_array

# Constantes a convertir
app = "4e69df1764941278"
dev = "34e726b6e74d6d89"
app_key = "7579e5b6122a454c7f770eccef44da2a"

# Conversión
app_converted = convert_to_decimal_array(app, reverse=True)
dev_converted = convert_to_decimal_array(dev, reverse=True)
app_key_converted = convert_to_decimal_array(app_key, reverse=False)

# Formatear la salida en JSON con comillas dobles y sin espacios adicionales
result = {
    "APPEUI": app_converted,
    "DEVEUI": dev_converted,
    "APPKEY": app_key_converted
}

print(json.dumps(result, separators=(',', ':')))
