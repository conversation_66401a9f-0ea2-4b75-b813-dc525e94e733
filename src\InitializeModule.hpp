// src\InitializeModule.hpp
#include "../lib/version/buildinfo.hpp"

#include "AllModule/RGB/RGBFunctions.hpp"
#include "AllModule/SD/SDFunctions.hpp"
#include "AllModule/WiFi/WiFiFunctions.hpp"
#include "AllModule/Updates/UpdatesFunctions.hpp"
#include "AllNode/RS485/RS485Functions.hpp"
#include "AllModule/WebSockets/WebSocketsFunctions.hpp"
#include "AllModule/WebServer/WebServerFunctions.hpp"

#include "AllNode/RS485/LinkSensorFunctions.hpp"
#include "AllNode/RS485/RoutineSensorFunctions.hpp"
#include "AllNode/LoRa/LoRaMessageFunctions.hpp"
#include "AllNode/LoRaWAN/LoRaWANFunctions.hpp"
#include "AllNode/Sleep/SleepFunctions.hpp"

#include "AllModule/Button/ButtonFunctions.hpp"

void initializeModule()
{
    Serial.begin(115200);
    while (!Serial)
        ;
    Serial.println("Firmware Version: " + String(latestBuildTag));

    pinMode(ACTIVATION_PIN, OUTPUT);
    digitalWrite(ACTIVATION_PIN, HIGH);

    delay(500);

    // RS485
    initializeRS485();

    // RGB
    initializeRGB();
    setColor(BLACK);

    // BUTTON
    initializeButton();

#ifdef ENV_WROOM
    // SPI
    SPI.begin(SCK_PIN, MISO_PIN, MOSI_PIN);
    pinMode(SD_CS_PIN, OUTPUT);
    digitalWrite(SD_CS_PIN, HIGH);
    pinMode(LORA_CS_PIN, OUTPUT);
    digitalWrite(LORA_CS_PIN, HIGH);
#endif

    // SD
    initializeSD();

    // Inicializar SPIFFS
    if (!SPIFFS.begin(true))
    {
        Serial.println(F("An Error has occurred while mounting SPIFFS"));
        return;
    }

    // SYSTEM CONFIG
    if (!loadSystemInfo(SYSTEM_INFO_PATH, systemInfo))
    {
        Serial.println("Failed to load system config. Using default values.");
    }
    else
    {
        Serial.println("System config loaded successfully.");
    }

    sensorRoutineCallback();
    sensorRoutineCallback();

    // ROUTINE SENSOR
    tSensorRoutine.enable();
    // tSendLoRaMessages.enable();

    // SLEEP MODE
    initializeSleepConfig();

    // LORAWAN
    initalizeLoRaWAN();
}

void loopModule()
{
    manageOTAUpdate();
    manageSleepMode();
    checkButtonPress();
    loopWebSockets();
    loopLoRaWAN();
    runner.execute();
}