// src\AllModule\WebSockets\CredentialsEventFunctions.hpp
#ifndef CREDENTIALSEVENTFUNCTIONS_HPP
#define CREDENTIALSEVENTFUNCTIONS_HPP

void handleGetCredentials(AsyncWebSocketClient *client)
{
    File file = SD.open(CREDENTIALS_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open credentials file.");
        client->text("{\"type\":\"credentials\",\"ssid\":\"unknown\",\"password\":\"unknown\"}");
        return;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse credentials file.");
        client->text("{\"type\":\"credentials\",\"ssid\":\"unknown\",\"password\":\"unknown\"}");
        return;
    }

    JsonDocument responseDoc;
    responseDoc["type"] = "credentials";
    responseDoc["ssid"] = doc["ssid"].as<String>();
    responseDoc["password"] = doc["password"].as<String>();

    String jsonResponse;
    serializeJson(responseDoc, jsonResponse);
    client->text(jsonResponse);
}

void handleUpdateCredentials(AsyncWebSocketClient *client, const String &msg)
{
    JsonDocument requestDoc;
    DeserializationError error = deserializeJson(requestDoc, msg);
    if (error)
    {
        Serial.println("Failed to parse update credentials message.");
        client->text("{\"type\":\"update_status\",\"status\":\"failed\"}");
        return;
    }

    String newSSID = requestDoc["ssid"];
    String newPassword = requestDoc["password"];

    File file = SD.open(CREDENTIALS_PATH, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open credentials file for writing.");
        client->text("{\"type\":\"update_status\",\"status\":\"failed\"}");
        return;
    }

    JsonDocument doc;
    doc["ssid"] = newSSID;
    doc["password"] = newPassword;

    serializeJson(doc, file);
    file.close();

    client->text("{\"type\":\"update_status\",\"status\":\"success\"}");
}

#endif // CREDENTIALSEVENTFUNCTIONS_HPP