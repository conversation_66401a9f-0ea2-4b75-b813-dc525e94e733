// src\AllNode\RS485\RS485Functions.hpp
#ifndef RS485FUNCTIONS_HPP
#define RS485FUNCTIONS_HPP

void initializeRS485()
{
#ifdef ENV_WROOM
    rs485Serial.begin(BAUD_RS485, SERIAL_8N1, RX_RS485_PIN, TX_RS485_PIN);
#elif defined(ENV_TTGO)
    rs485Serial.begin(BAUD_RS485);
#endif
}

uint16_t calculateCRC(uint8_t *data, uint8_t length)
{
    uint16_t crc = 0xFFFF;
    for (uint8_t i = 0; i < length; i++)
    {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++)
        {
            if (crc & 0x0001)
            {
                crc >>= 1;
                crc ^= 0xA001;
            }
            else
            {
                crc >>= 1;
            }
        }
    }
    return crc;
}

void sendSlaveIDRequest(uint8_t slaveAddress, uint8_t readRegister, uint8_t highRegister, uint8_t lowRegister)
{
    uint8_t request[8] = {slaveAddress, readRegister, highRegister, lowRegister, 0x00, 0x01};
    uint16_t crc = calculateCRC(request, 6);
    request[6] = lowByte(crc);
    request[7] = highByte(crc);

    rs485Serial.write(request, 8);
}

void setSlaveID(uint8_t currentID, uint8_t newID)
{
    uint8_t request[8] = {currentID, 0x06, 0x07, 0xD0, 0x00, newID};
    uint16_t crc = calculateCRC(request, 6);
    request[6] = lowByte(crc);
    request[7] = highByte(crc);

    rs485Serial.write(request, 8);
}

#endif // RS485FUNCTIONS_HPP
