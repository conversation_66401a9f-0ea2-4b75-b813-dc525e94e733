# Gapy Node

This project is a monitoring system based on the ESP32, designed to obtain data from sensors through the RS485 bus and transmit it via LoRa. The system integrates environmental and soil temperature and humidity sensors, soil nutrient sensors, as well as air quality sensors.

The system is configured to transmit data for periods of time and enter a low power mode. The data collected from the sensors is stored in CSV files on an SD card for further analysis.

## lib/version

The `lib/version` folder contains files related to the versioning and build information of the Gapy Node firmware. This information is crucial for tracking the firmware version, build time, and the specific commit from which the firmware was built.

### buildinfo.hpp

This file is generated by the `generate_buildinfo.py` script located in the `scripts` folder. It contains metadata about the firmware build, such as the latest build tag, version digits, last commit tag, and build timestamp.

#### Variables

- **latestBuildTag**: Indicates the latest release tag of the firmware (e.g., "0.8.0").
- **mainVersionDigit**: The main version number (e.g., 0).
- **minorVersionDigit**: The minor version number (e.g., 8).
- **patchVersionDigit**: The patch version number (e.g., 0).
- **lastCommitTag**: The short hash of the latest commit used for the build (e.g., "738c3e6").
- **buildTimeStamp**: The timestamp of when the build was created (e.g., "2024-Jul-22 13:58:18").
