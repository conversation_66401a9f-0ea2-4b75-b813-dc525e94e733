// // src\AllNode\LoRa\LoRaFunctions.hpp
// #ifndef LORAFUNCTIONS_HPP
// #define LORAFUNCTIONS_HPP

// void initializeLoRa()
// {
//     LoRa.setPins(LORA_CS_PIN, LORA_RST_PIN, LORA_DIO0_PIN);

//     if (!LoRa.begin(915E6))
//     {
//         Serial.println(ERR_LORA_FAILED);
//         while (1)
//             ;
//     }
//     else
//     {
//         Serial.println(STA_LORA_INIT_OK);
//     }
// }

// void sendLoRaMessage(const String &message)
// {
//     LoRa.beginPacket();
//     LoRa.print(message);
//     LoRa.endPacket();
// }

// #endif // src\AllNode\LoRa\LoRaFunctions.hpp