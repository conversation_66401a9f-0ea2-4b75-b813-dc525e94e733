// src\AllNode\WiFi\WiFiFunctions.hpp
#ifndef WIFIFUNCTIONS_HPP
#define WIFIFUNCTIONS_HPP

bool loadWiFiCredentials(const char *path, WiFiCredentials &credentials)
{
    File file = SD.open(path, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open credentials file");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse credentials file");
        return false;
    }

    credentials.ssid = doc["ssid"].as<String>();
    credentials.password = doc["password"].as<String>();

    return true;
}

void connectWiFi()
{
    Serial.println("Connecting to WiFi...");

    WiFi.begin(wifiCredentials.ssid.c_str(), wifiCredentials.password.c_str());

    unsigned long startAttemptTime = millis();

    // Esperar a que WiFi se conecte (con un tiempo límite)
    while (WiFi.status() != WL_CONNECTED && millis() - startAttemptTime < 10000)
    {
        delay(100);
        Serial.print(".");
    }

    if (WiFi.status() == WL_CONNECTED)
    {
        Serial.println("\nWiFi connected!");
        Serial.print("IP address: ");
        Serial.println(WiFi.localIP());
    }
    else
    {
        Serial.println("\nFailed to connect to WiFi.");
        // Connect to Wi-Fi network with SSID and password
        Serial.print("Setting AP (Access Point)…");
        // Remove the password parameter, if you want the AP (Access Point) to be open
        WiFi.softAP("gapy_node", "gapy1234");

        IPAddress IP = WiFi.softAPIP();
        Serial.print("AP IP address: ");
        Serial.println(IP);
    }
}

void disconnectWiFi()
{
    Serial.println("Disconnecting WiFi...");
    WiFi.disconnect();
    Serial.println("WiFi disconnected.");
}

void handleWiFi()
{
    if (wifiEnabled)
    {
        Serial.println("Enabled WiFi");
        if (loadWiFiCredentials(CREDENTIALS_PATH, wifiCredentials))
        {
            connectWiFi();
        }
        else
        {
            Serial.println("Failed to load WiFi credentials. Disabling WiFi.");
            wifiEnabled = false;
        }
    }
    else
    {
        Serial.println("Disabled WiFi");
        disconnectWiFi();
    }
}

void setWiFiEnabled(bool enabled)
{
    wifiEnabled = enabled;
    handleWiFi();
}

#endif // WIFIFUNCTIONS_HPP
