// src\AllNode\LoRaWAN\LoRaWANFunctions.hpp
#ifndef LORAWANFUNCTIONS_HPP
#define LORAWANFUNCTIONS_HPP

#include "../Common/FileRecoveryFunctions.hpp"

void printHex2(unsigned v)
{
    v &= 0xff;
    if (v < 16)
        Serial.print('0');
    Serial.print(v, HEX);
}

bool loadLoRaWANConfig(LoRaWANConfig &loRaWANConfig)
{
    JsonDocument doc;
    if (!safeReadJsonFile(LORAWAN_CONFIG_PATH, doc))
    {
        Serial.println("Failed to read lorawan_config.json with recovery");
        return false;
    }

    loRaWANConfig.TX_INTERVAL = doc["txInterval"];
    loRaWANConfig.DELAY_LORAWAN = doc["delayLorawan"];

    Serial.println("LoRaWAN config loaded successfully");
    return true;
}

// Function to read LoRaWAN credentials from SD
bool loadLoRaWANCredentials()
{
    JsonDocument doc;
    if (!safeReadJsonFile(LORAWAN_CREDENTIALS_PATH, doc))
    {
        Serial.println("Failed to read lorawan_credentials.json with recovery");
        return false;
    }

    JsonArray appeui = doc["APPEUI"].as<JsonArray>();
    JsonArray deveui = doc["DEVEUI"].as<JsonArray>();
    JsonArray appkey = doc["APPKEY"].as<JsonArray>();

    for (int i = 0; i < 8; i++)
    {
        lorawanAppeui[i] = appeui[i];
        lorawanDeveui[i] = deveui[i];
    }

    for (int i = 0; i < 16; i++)
    {
        lorawanAppkey[i] = appkey[i];
    }

    Serial.println("LoRaWAN credentials loaded successfully");
    return true;
}

// Override functions to provide credentials
void os_getArtEui(u1_t *buf) { memcpy(buf, lorawanAppeui, 8); }
void os_getDevEui(u1_t *buf) { memcpy(buf, lorawanDeveui, 8); }
void os_getDevKey(u1_t *buf) { memcpy(buf, lorawanAppkey, 16); }

void saveSession()
{
    u4_t netid;
    devaddr_t devaddr;
    u1_t nwkKey[16], artKey[16];
    u4_t fCntUp = LMIC.seqnoUp;

    // Obtener las llaves de la sesión
    LMIC_getSessionKeys(&netid, &devaddr, nwkKey, artKey);

    Serial.println("Saving LoRaWAN session to SD...");

    // Crear un documento JSON para almacenar la sesión
    JsonDocument doc;
    doc["netid"] = netid;
    doc["devaddr"] = devaddr;
    JsonArray nwkKeyArray = doc["nwkKey"].to<JsonArray>();
    JsonArray artKeyArray = doc["artKey"].to<JsonArray>();

    // Añadir las llaves de red y aplicación al JSON
    for (int i = 0; i < 16; i++)
    {
        nwkKeyArray.add(nwkKey[i]);
        artKeyArray.add(artKey[i]);
    }

    doc["fCntUp"] = fCntUp;

    // Abrir el archivo en la SD para escribir la sesión
    File file = SD.open(LORAWAN_SESSION_PATH, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open lorawan_session.json for writing");
        return;
    }

    // Escribir el JSON en el archivo
    serializeJson(doc, file);
    file.close();

    Serial.println("LoRaWAN session saved successfully.");
}

bool restoreSession()
{
    Serial.println("Restoring LoRaWAN session from SD...");

    // Verificar si el archivo de sesión existe en la SD
    if (!SD.exists(LORAWAN_SESSION_PATH))
    {
        Serial.println("No session file found on SD");
        return false;
    }

    // Abrir el archivo para lectura
    File file = SD.open(LORAWAN_SESSION_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open lorawan_session.json for reading");
        return false;
    }

    // Crear un documento JSON para leer la sesión
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    // Verificar si hubo algún error al leer el JSON
    if (error)
    {
        Serial.println("Failed to parse lorawan_session.json");
        return false;
    }

    // Restaurar la sesión desde el JSON
    u4_t netid = doc["netid"];
    devaddr_t devaddr = doc["devaddr"];
    u1_t nwkKey[16], artKey[16];
    JsonArray nwkKeyArray = doc["nwkKey"];
    JsonArray artKeyArray = doc["artKey"];

    for (int i = 0; i < 16; i++)
    {
        nwkKey[i] = nwkKeyArray[i];
        artKey[i] = artKeyArray[i];
    }

    u4_t fCntUp = doc["fCntUp"];

    // Configurar la sesión de LMIC con los valores restaurados
    LMIC_setSession(netid, devaddr, nwkKey, artKey);
    LMIC.seqnoUp = fCntUp;

    Serial.println("LoRaWAN session restored successfully.");
    return true;
}

void deleteSession()
{
    // Verificar si el archivo de sesión existe
    if (SD.exists(LORAWAN_SESSION_PATH))
    {
        // Eliminar el archivo de sesión
        if (SD.remove(LORAWAN_SESSION_PATH))
        {
            Serial.println("LoRaWAN session deleted successfully.");
        }
        else
        {
            Serial.println("Failed to delete LoRaWAN session.");
        }
    }
    else
    {
        Serial.println("No LoRaWAN session found to delete.");
    }
}

void do_send(osjob_t *j)
{
    if (LMIC.opmode & OP_TXRXPEND)
    {
        Serial.println(F("OP_TXRXPEND, not sending"));
    }
    else
    {
        Serial.println(F("Starting LoRa message sending task."));

        JsonDocument scanRegister = readScanRegister();
        JsonArray ids = scanRegister["ids"];

        uint8_t payload[PAYLOAD_MAX_SIZE];
        uint8_t payloadIndex = 0;

        for (JsonObject idEntry : ids)
        {
            uint8_t idSensor = idEntry["id"];
            uint8_t typeSensor = idEntry["type"];
            uint8_t readAddress = idEntry["readRegister"];
            uint8_t writeAddress = idEntry["writeRegister"];
            uint16_t idAddress = strtol(idEntry["idRegister"], nullptr, 0);
            bool state = checkSensorState(idSensor, readAddress, idAddress);

            if (payloadIndex + 2 > PAYLOAD_MAX_SIZE)
                break;
            payload[payloadIndex++] = idSensor;
            payload[payloadIndex++] = typeSensor;
            payload[payloadIndex++] = state ? 0x01 : 0x00;

            if (state)
            {
                JsonArray paramNames = idEntry["parameters"];
                for (JsonVariant paramName : paramNames)
                {
                    String filePath = "/sensor_" + String(idSensor) + "_" + paramName.as<String>() + ".json";
                    JsonDocument sensorData = readSensorData(filePath.c_str());

                    float data = 0;
                    if (typeSensor == 1 || typeSensor == 2 || typeSensor == 3)
                    {
                        data = sensorData["value"].as<float>();
                    }
                    else
                    {
                        data = sensorData["value"].as<float>() + sensorData["offset"].as<float>();
                    }

                    float factor = sensorData["factor"].as<float>();
                    uint16_t scaledData = static_cast<uint16_t>(data * factor);

                    if (payloadIndex + 2 > PAYLOAD_MAX_SIZE)
                        break;
                    payload[payloadIndex++] = (scaledData >> 8) & 0xFF;
                    payload[payloadIndex++] = scaledData & 0xFF;
                }
            }
        }

        if (LMIC.opmode & OP_TXRXPEND)
        {
            Serial.println(F("OP_TXRXPEND, not sending"));
        }
        else
        {
            LMIC_setTxData2(1, payload, payloadIndex, 0);
            Serial.println(F("Packet queued"));

            Serial.print("Payload: ");
            for (int i = 0; i < payloadIndex; i++)
            {
                if (i > 0)
                    Serial.print(" ");
                Serial.print(payload[i], HEX);
            }
            Serial.println();
        }

        Serial.println(F("LoRa message sending task completed."));
    }
}

void onEvent(ev_t ev)
{
    Serial.print(os_getTime());
    Serial.print(": ");
    switch (ev)
    {
    case EV_SCAN_TIMEOUT:
        Serial.println(F("EV_SCAN_TIMEOUT"));
        break;
    case EV_BEACON_FOUND:
        Serial.println(F("EV_BEACON_FOUND"));
        break;
    case EV_BEACON_MISSED:
        Serial.println(F("EV_BEACON_MISSED"));
        break;
    case EV_BEACON_TRACKED:
        Serial.println(F("EV_BEACON_TRACKED"));
        break;
    case EV_JOINING:
        Serial.println(F("EV_JOINING"));
        break;
    case EV_JOINED:
        Serial.println(F("EV_JOINED"));
        {
            saveSession();
        }
        LMIC_setLinkCheckMode(0);
        break;
    case EV_JOIN_FAILED:
        Serial.println(F("EV_JOIN_FAILED"));
        break;
    case EV_REJOIN_FAILED:
        Serial.println(F("EV_REJOIN_FAILED"));
        break;
    case EV_TXCOMPLETE:
        Serial.println(F("EV_TXCOMPLETE (includes waiting for RX windows)"));
        if (LMIC.txrxFlags & TXRX_ACK)
        {
            Serial.println(F("Received ack"));
        }
        if (LMIC.dataLen)
        {
            Serial.print(F("Received "));
            Serial.print(LMIC.dataLen);
            Serial.println(F(" bytes of payload"));

            Serial.print(F("Payload: "));
            for (uint8_t i = 0; i < LMIC.dataLen; i++)
            {
                if (i > 0)
                {
                    Serial.print(" ");
                }
                Serial.print(LMIC.frame[LMIC.dataBeg + i], HEX);
            }
            Serial.println();

            receivedPayloadLength = LMIC.dataLen;
            memcpy(receivedPayload, &LMIC.frame[LMIC.dataBeg], receivedPayloadLength);

            commandReceivedFlag = true;
        }
        saveSession();
        os_setTimedCallback(&sendjob, os_getTime() + sec2osticks(loRaWANConfig.TX_INTERVAL), do_send);
        break;
    case EV_LOST_TSYNC:
        Serial.println(F("EV_LOST_TSYNC"));
        break;
    case EV_RESET:
        Serial.println(F("EV_RESET"));
        break;
    case EV_RXCOMPLETE:
        // data received in ping slot
        Serial.println(F("EV_RXCOMPLETE"));
        break;
    case EV_LINK_DEAD:
        Serial.println(F("EV_LINK_DEAD"));
        break;
    case EV_LINK_ALIVE:
        Serial.println(F("EV_LINK_ALIVE"));
        break;
    case EV_TXSTART:
        Serial.println(F("EV_TXSTART"));
        break;
    case EV_TXCANCELED:
        Serial.println(F("EV_TXCANCELED"));
        break;
    case EV_RXSTART:
        break;
    case EV_JOIN_TXCOMPLETE:
        Serial.println(F("EV_JOIN_TXCOMPLETE: no JoinAccept"));
        break;

    default:
        Serial.print(F("Unknown event: "));
        Serial.println((unsigned)ev);
        break;
    }
}

void initalizeLoRaWAN()
{
    os_init();
    LMIC_reset();

    // Load credentials from SD card before joining
    if (!loadLoRaWANCredentials())
    {
        Serial.println(F("Failed to load LoRaWAN credentials, aborting"));
        return;
    }

    if (!loadLoRaWANConfig(loRaWANConfig))
    {
        Serial.println("Failed to load LoRaWAN config. Using default values.");
    }
    else
    {
        Serial.println("LoRaWAN config loaded successfully.");
    }

    if (restoreSession())
    {
        Serial.println(F("Session restored, skipping join."));
    }
    else
    {
        Serial.println(F("No session found, starting join process."));
        LMIC_startJoining();
    }

    LMIC_setLinkCheckMode(0);
    LMIC_setDrTxpow(LMIC.datarate, LMIC.txpow);
    LMIC_selectSubBand(1);

    // sensorRoutineCallback();

    // do_send(&sendjob);
    #ifdef SLEEP_MODE
    os_setTimedCallback(&sendjob, os_getTime() + sec2osticks(loRaWANConfig.DELAY_LORAWAN), do_send);
    #else
    do_send(&sendjob);
    #endif
}

bool writeOffsetSensor(uint8_t slaveAddress, const String &parameterFilePath, float &value)
{
    Serial.print("Reading sensor data for slave: ");
    Serial.println(slaveAddress);

    JsonDocument doc;
    File file = SD.open(parameterFilePath, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (!error)
        {
            Serial.print("Deserialized parameter file: ");
            Serial.println(parameterFilePath);

            uint16_t offsetAddress = strtol(doc["offset_address"], nullptr, 0);
            Serial.print("Using register address: ");
            Serial.println(offsetAddress, HEX);

            float factor = doc["factor"] | 1.0;
            int16_t scaledData = static_cast<int16_t>(value * factor);

            uint8_t msb = (scaledData >> 8) & 0xFF;
            uint8_t lsb = scaledData & 0xFF;

            uint8_t request[8] = {slaveAddress, 0x06, highByte(offsetAddress), lowByte(offsetAddress), msb, lsb};
            uint16_t crc = calculateCRC(request, 6);
            request[6] = lowByte(crc);
            request[7] = highByte(crc);

            Serial.print("Request offset: ");
            for (int i = 0; i < 8; i++)
            {
                Serial.print(request[i], HEX);
                Serial.print(" ");
            }
            Serial.println();

            rs485Serial.write(request, 8);

            delay(300);
            if (rs485Serial.available())
            {
                uint8_t response[8];
                for (uint8_t i = 0; i < 8; i++)
                {
                    response[i] = rs485Serial.read();
                }

                uint16_t crcResponse = calculateCRC(response, 6);
                if (response[0] == slaveAddress && crcResponse == (response[7] << 8 | response[6]))
                {
                    int16_t rawData = (response[4] << 8) | response[5];
                    Serial.print("Read sensor data successfully: ");
                    Serial.println(static_cast<float>(rawData) / factor);
                    return true;
                }
                else
                {
                    Serial.print("Invalid CRC response when reading sensor data from slave: ");
                    Serial.println(slaveAddress);
                }
            }
            else
            {
                Serial.print("No response when reading sensor data from slave: ");
                Serial.println(slaveAddress);
            }
        }
        else
        {
            Serial.print("Failed to deserialize parameter file: ");
            Serial.println(parameterFilePath);
        }
    }
    else
    {
        Serial.print("Failed to open parameter file: ");
        Serial.println(parameterFilePath);
    }
    return false;
}

bool storeOffsetInParameter(uint8_t idSensor, const String &parameterName, float offset)
{
    String filePath = "/sensor_" + String(idSensor) + "_" + parameterName + ".json";
    JsonDocument doc;

    File file = SD.open(filePath, FILE_READ);
    if (!file)
    {
        Serial.print("Error al abrir el archivo para lectura: ");
        Serial.println(filePath);
        return false;
    }

    DeserializationError error = deserializeJson(doc, file);
    file.close();
    if (error)
    {
        Serial.print("Error al deserializar el archivo del sensor: ");
        Serial.println(filePath);
        return false;
    }

    doc["offset"] = offset;

    file = SD.open(filePath, FILE_WRITE);
    if (!file)
    {
        Serial.print("Error al abrir el archivo para escritura: ");
        Serial.println(filePath);
        return false;
    }

    serializeJson(doc, file);
    file.close();

    Serial.print("Offset guardado correctamente en: ");
    Serial.println(filePath);
    return true;
}

float decodeOffset(uint8_t sign, int16_t value, uint8_t idSensor, const String &parameterName)
{
    String filePath = "/sensor_" + String(idSensor) + "_" + parameterName + ".json";
    JsonDocument doc;
    if (!safeReadJsonFile(filePath.c_str(), doc))
    {
        Serial.print("Error al leer el archivo del sensor con recuperación: ");
        Serial.println(filePath);
        return 0;
    }

    float factor = doc["factor"].as<float>();

    float decodedValue = value / factor;
    if (sign == 0x01)
    {
        decodedValue = -decodedValue;
    }

    Serial.print("Offset decodificado para ");
    Serial.print(parameterName);
    Serial.print(": ");
    Serial.println(decodedValue);

    return decodedValue;
}

JsonObject findSensorById(JsonDocument &doc, uint8_t idSensor)
{
    JsonArray ids = doc["ids"].as<JsonArray>();
    for (JsonObject sensor : ids)
    {
        if (sensor["id"] == idSensor)
        {
            return sensor;
        }
    }
    return JsonObject();
}

JsonDocument readScanRegisterLoRaWAN()
{
    JsonDocument doc;
    if (!safeReadJsonFile(SCAN_REGISTER_PATH, doc))
    {
        Serial.println("Error al leer scan_register.json con recuperación.");
    }
    return doc;
}

void processOffsetsFromCommand()
{
    uint8_t index = 0; // Índice actual en el payload recibido

    while (index < receivedPayloadLength)
    {
        uint8_t commandType = receivedPayload[index++];

        if (commandType != 0x01)
        {
            Serial.println("Comando no reconocido, se espera un comando de tipo offset.");
            return; // Solo procesamos el comando de offset, otros tipos no son reconocidos.
        }

        while (index < receivedPayloadLength)
        {
            uint8_t idSensor = receivedPayload[index++];
            Serial.print("Procesando ID de sensor: ");
            Serial.println(idSensor);

            // Buscar en scan_register.json el idSensor
            JsonDocument scanRegister = readScanRegisterLoRaWAN();
            JsonObject sensorInfo = findSensorById(scanRegister, idSensor);

            if (sensorInfo.isNull())
            {
                Serial.print("No se encontró información para el sensor ID: ");
                Serial.println(idSensor);
                continue;
            }

            uint8_t typeSensor = sensorInfo["type"];
            JsonArray parameters = sensorInfo["parameters"].as<JsonArray>();

            for (size_t i = 0; i < parameters.size(); ++i)
            {
                if (index + 3 > receivedPayloadLength)
                {
                    Serial.println("Error: el payload no tiene suficientes datos para los parámetros esperados.");
                    return;
                }

                String parameterName = parameters[i].as<String>();
                Serial.print("Procesando parámetro: ");
                Serial.println(parameterName);

                // Leer los 3 bytes del offset
                uint8_t sign = receivedPayload[index++];
                uint8_t lsb = receivedPayload[index++];
                uint8_t msb = receivedPayload[index++];

                // Convertir el valor a decimal
                int16_t offsetValue = ((lsb << 8) | msb);
                float decodedOffset = decodeOffset(sign, offsetValue, idSensor, parameterName);

                // Guardar el offset en el JSON del parámetro
                if (!storeOffsetInParameter(idSensor, parameterName, decodedOffset))
                {
                    Serial.println("Error al guardar el offset en el archivo JSON.");
                }

                if (typeSensor == 1 || typeSensor == 2 || typeSensor == 3)
                {
                    Serial.println("Set offset for command RS485");
                    String filePath = "/sensor_" + String(idSensor) + "_" + parameterName + ".json";
                    if (writeOffsetSensor(idSensor, filePath, decodedOffset))
                    {
                        Serial.println("Offset OK");
                    }
                }
            }
        }
    }
    Serial.println("Comando procesado exitosamente.");
}

void processCommandLoRaWAN()
{
    if (receivedPayloadLength > 0)
    {
        Serial.println(F("Processing received command..."));

        switch (receivedPayload[0])
        {
        case ID_COMMAND:
            Serial.println(F("Received command: Offset command received"));
            processOffsetsFromCommand();
            break;

        default:
            Serial.println(F("Received command: Unknown command"));
            break;
        }

        receivedPayloadLength = 0;

        commandReceivedFlag = false;
    }
    else
    {
        Serial.println(F("No command to process"));
    }
}

void loopLoRaWAN()
{
    if (commandReceivedFlag)
    {
        processCommandLoRaWAN();
    }
    os_runloop_once();
}

#endif // src\AllNode\LoRaWAN\LoRaWANFunctions.hpp