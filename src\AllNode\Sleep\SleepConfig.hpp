// src\AllNode\Sleep\SleepConfig.hpp
#ifndef SLEEPCONFIG_HPP
#define SLEEPCONFIG_HPP

#ifdef ENV_WROOM
#define INTERRUPT_PIN 15
#elif defined(ENV_TTGO)
#define INTERRUPT_PIN 25
#endif

#define uS_TO_S_FACTOR 1000000ULL
#define SLEEP_CONFIG_PATH "/sleep_config.json"

struct SleepConfig
{
    unsigned long timeToSleep;
    unsigned long timeActiveNode;
    bool sleepModeActiveFlag;
};

SleepConfig sleepConfig = {600, 20, true};

unsigned long previousMillis = 0;
bool goToSleepFlag = false;

#endif // SLEEPCONFIG_HPP
