// src\AllNode\LoRa\LoRaMessageConfig.hpp
#ifndef LORAMESSAGECONFIG_HPP
#define LORAMESSAGECONFIG_HPP

#define SYSTEM_INFO_PATH "/system_info.json"

struct SystemInfo
{
    String model;
    String version;
    String deviceId;
};

SystemInfo systemInfo = {"gapy_node", "0.0.0", "GNODE_DEFAULT"};

bool sendMessageLoRaFlag = false;

// void sendLoRaMessagesCallback();

// Task tSendLoRaMessages(TASK_SECOND * 30, TASK_FOREVER, &sendLoRaMessagesCallback, &runner);

#endif // LORAMESSAGECONFIG_HPP

