// src\AllNode\RS485\LinkSensorFunctions.hpp
#ifndef LINKSENSORFUNCTIONS_HPP
#define LINKSENSORFUNCTIONS_HPP

#include "../Common/FileRecoveryFunctions.hpp"

bool createScanRegisterFile(const char *path)
{
    File file = SD.open(path, FILE_WRITE);
    if (file)
    {
        file.println("{}");
        file.close();
        return true;
    }
    return false;
}

bool updateScanRegisterFile(const char *path, uint8_t id, uint8_t type, JsonArray parameterNames, uint8_t readRegister, uint8_t writeRegister, const char *idRegister)
{
    JsonDocument doc;
    if (!safeReadJsonFile(path, doc))
    {
        // Si no se puede leer, crear estructura básica
        doc.clear();
    }

    JsonArray ids = doc["ids"];
    if (!ids)
    {
        ids = doc["ids"].to<JsonArray>();
    }

    bool idExists = false;
    for (JsonObject existingId : ids)
    {
        if (existingId["id"] == id)
        {
            idExists = true;
            break;
        }
    }

    if (!idExists)
    {
        JsonObject newId = ids.add<JsonObject>();
        newId["id"] = id;
        newId["type"] = type;
        newId["readRegister"] = readRegister;
        newId["writeRegister"] = writeRegister;
        newId["idRegister"] = idRegister;
        JsonArray paramsArray = newId["parameters"].to<JsonArray>();
        for (JsonVariant paramName : parameterNames)
        {
            paramsArray.add(paramName.as<String>());
        }

        return safeWriteJsonFile(path, doc);
    }
    return false;
}

bool deleteScanRegisterFile(const char *path)
{
    return SD.remove(path);
}

bool createParameterFile(const char *path)
{
    File file = SD.open(path, FILE_WRITE);
    if (file)
    {
        file.println("{}");
        file.close();
        return true;
    }
    return false;
}

bool updateParameterFile(const char *path, const JsonObject &data)
{
    File file = SD.open(path, FILE_WRITE);
    if (file)
    {
        serializeJson(data, file);
        file.close();
        return true;
    }
    return false;
}

bool deleteParameterFile(const char *path)
{
    return SD.remove(path);
}

void generateRegistersSensor(uint8_t slaveAddress, uint8_t commonAddress)
{
    JsonDocument sensorDB;
    if (!safeReadJsonFile(SENSOR_DB_PATH, sensorDB))
    {
        Serial.println("Failed to read sensor database");
        return;
    }

    JsonObject sensor;
    for (JsonObject item : sensorDB["sensors"].as<JsonArray>())
    {
        if (item["id"] == commonAddress)
        {
            sensor = item;
            break;
        }
    }

    if (!sensor.isNull())
    {
        for (JsonObject parameter : sensor["parameters"].as<JsonArray>())
        {
            String filePath = "/sensor_" + String(slaveAddress) + "_" + String(parameter["name"].as<String>()) + ".json";
            createParameterFile(filePath.c_str());
            updateParameterFile(filePath.c_str(), parameter);
        }
    }
}

uint8_t getTotalSensors(const char *path)
{
    JsonDocument doc;
    if (safeReadJsonFile(path, doc))
    {
        return doc["sensors"].size();
    }
    return 0;
}

std::vector<uint8_t> readRegisteredIDs(const char *path)
{
    std::vector<uint8_t> ids;
    JsonDocument doc;
    if (safeReadJsonFile(path, doc))
    {
        JsonArray jsonIds = doc["ids"];
        for (JsonObject idObj : jsonIds)
        {
            ids.push_back(idObj["id"].as<uint8_t>());
        }
    }
    return ids;
}

uint8_t generateNewID()
{
    std::vector<uint8_t> registeredIDs = readRegisteredIDs(SCAN_REGISTER_PATH);
    uint8_t totalSensors = getTotalSensors(SENSOR_DB_PATH);

    uint8_t newID = totalSensors + 1;

    while (std::find(registeredIDs.begin(), registeredIDs.end(), newID) != registeredIDs.end())
    {
        newID++;
    }

    return newID;
}

void deleteParameterFiles(uint8_t slaveAddress)
{
    JsonDocument scanRegister;
    if (safeReadJsonFile(SCAN_REGISTER_PATH, scanRegister))
    {
            JsonArray ids = scanRegister["ids"];
            for (JsonObject idEntry : ids)
            {
                if (idEntry["id"] == slaveAddress)
                {
                    JsonArray parameters = idEntry["parameters"];
                    for (JsonVariant parameter : parameters)
                    {
                        String filePath = "/sensor_" + String(slaveAddress) + "_" + parameter.as<String>() + ".json";
                        if (SD.exists(filePath.c_str()))
                        {
                            SD.remove(filePath.c_str());
                            Serial.print("Deleted file: ");
                            Serial.println(filePath);
                        }
                    }
                    break;
                }
            }
    }
}

bool clearScanRegisterFile(const char *path)
{
    JsonDocument doc;
    if (!safeReadJsonFile(path, doc))
    {
        // Si no se puede leer, crear estructura básica
        doc.clear();
    }

    JsonArray ids = doc["ids"];
    if (ids)
    {
        ids.clear();
    }

    return safeWriteJsonFile(path, doc);
}

void resetAllSensors()
{
    std::vector<uint8_t> registeredIDs = readRegisteredIDs(SCAN_REGISTER_PATH);

    for (uint8_t id : registeredIDs)
    {
        deleteParameterFiles(id);
    }

    clearScanRegisterFile(SCAN_REGISTER_PATH);
    clearScanRegisterFile(SENSOR_AVAILABLE_PATH);
    clearScanRegisterFile(CARDS_AVAILABLE_PATH);

    Serial.println("All sensor files deleted and scan register reset.");
}

uint8_t scanIdRegister()
{
    uint8_t addrRegister = 0;
    uint8_t commandParams[6] = {0xFF, 0x12, 0xFE, 0x0E, 0x0F, 0xFE};

    uint8_t readParams[6] = {0x03, 0x03, 0x11, 0x03, 0x03, 0x03};
    uint8_t highParams[6] = {0x07, 0x00, 0x00, 0x00, 0x00, 0x00};
    uint8_t lowParams[6] = {0xD0, 0x30, 0x00, 0x00, 0x01, 0x02};

    uint8_t numParams = sizeof(commandParams);

    for (uint8_t paramIndex = 0; paramIndex < numParams; paramIndex++)
    {
        sendSlaveIDRequest(commandParams[paramIndex], readParams[paramIndex], highParams[paramIndex], lowParams[paramIndex]);
        delay(100);

        if (rs485Serial.available())
        {
            uint8_t response[7];
            for (uint8_t i = 0; i < 7; i++)
            {
                response[i] = rs485Serial.read();
            }

            Serial.print("Response for ID with param ");
            Serial.print(commandParams[paramIndex], HEX);
            Serial.print(": ");
            for (int i = 0; i < 7; i++)
            {
                Serial.print(response[i], HEX);
                Serial.print(" ");
            }
            Serial.println();

            uint16_t crc = calculateCRC(response, 5);
            if (crc == (response[6] << 8 | response[5]))
            {
                addrRegister = response[0];
                break;
            }
        }
        else
        {
            Serial.print("No response for param ");
            Serial.println(commandParams[paramIndex], HEX);
        }
    }

    if (addrRegister == 0)
    {
        Serial.println("Unfound type device after all attempts");
    }

    return addrRegister;
}

void scanAndRegisterSensors()
{
    uint8_t addrRegister = 0;
    uint8_t newID = 0;

    addrRegister = scanIdRegister();

    if (addrRegister != 0)
    {
        Serial.print("Found device on address: ");
        Serial.println(addrRegister);

        JsonDocument sensorDB;
        if (!safeReadJsonFile(SENSOR_DB_PATH, sensorDB))
        {
            Serial.println("Failed to read sensor database");
            return;
        }

        JsonDocument scanRegister;

        uint8_t readRegister, writeRegister;
        const char *idRegister;

        JsonArray parameterNames = scanRegister.add<JsonArray>();
        for (JsonObject item : sensorDB["sensors"].as<JsonArray>())
        {
            if (item["id"] == addrRegister)
            {
                readRegister = item["readRegister"];
                writeRegister = item["writeRegister"];
                idRegister = item["idRegister"];
                for (JsonObject parameter : item["parameters"].as<JsonArray>())
                {
                    parameterNames.add(parameter["name"].as<String>());
                }
                break;
            }
        }

        if (!parameterNames.isNull() && updateScanRegisterFile(SCAN_REGISTER_PATH, addrRegister, addrRegister, parameterNames, readRegister, writeRegister, idRegister))
        {
            generateRegistersSensor(addrRegister, addrRegister);
            if (updateScanRegisterFile(SENSOR_AVAILABLE_PATH, addrRegister, addrRegister, parameterNames, readRegister, writeRegister, idRegister))
            {
                Serial.println("ID register added to sensors availables");
            }
        }
        else
        {
            Serial.println("Update ID");
            newID = generateNewID();
            Serial.print("New ID: ");
            Serial.println(newID);
            setSlaveID(addrRegister, newID);
            delay(100);

            while (rs485Serial.available())
            {
                uint8_t response[8];
                for (uint8_t i = 0; i < 8; i++)
                {
                    response[i] = rs485Serial.read();
                }

                Serial.print("Response for new ID: ");
                for (int i = 0; i < 8; i++)
                {
                    Serial.print(response[i], HEX);
                    Serial.print(" ");
                }
                Serial.println();

                uint16_t crc = calculateCRC(response, 6);
                if (crc == (response[7] << 8 | response[6]))
                {
                    Serial.print("ID successfully changed to ");
                    Serial.println(newID);
                }
            }

            sendSlaveIDRequest(newID, 0x03, 0x07, 0xD0);
            delay(100);

            while (rs485Serial.available())
            {
                uint8_t response[7];
                for (uint8_t i = 0; i < 7; i++)
                {
                    response[i] = rs485Serial.read();
                }

                Serial.print("Response for new ID: ");
                for (int i = 0; i < 7; i++)
                {
                    Serial.print(response[i], HEX);
                    Serial.print(" ");
                }
                Serial.println();

                uint16_t crc = calculateCRC(response, 5);
                if (response[0] == newID && crc == (response[6] << 8 | response[5]))
                {
                    Serial.println("ID register added");
                    if (updateScanRegisterFile(SCAN_REGISTER_PATH, newID, addrRegister, parameterNames, readRegister, writeRegister, idRegister))
                    {
                        generateRegistersSensor(newID, addrRegister);
                    }
                    if (updateScanRegisterFile(SENSOR_AVAILABLE_PATH, newID, addrRegister, parameterNames, readRegister, writeRegister, idRegister))
                    {
                        Serial.println("ID register added to sensors availables");
                    }
                }
                else
                {
                    Serial.println("Failed to confirm new ID.");
                }
            }

            Serial.println("Scan and registration process completed!");
        }
    }
}

#endif // LINKSENSORFUNCTIONS_HPP