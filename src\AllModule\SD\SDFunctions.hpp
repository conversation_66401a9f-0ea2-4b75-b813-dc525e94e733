// src\AllModule\SD\SDFunctions.hpp
#ifndef SDFUNCTIONS_HPP
#define SDFUNCTIONS_HPP

// Handles errors related to SD card initialization
// Prints an error message to the Serial monitor
void handleSDError()
{
    Serial.println(ERR_SD_NOT_DETECTED);
    sdNotDetected = true;
}

// Initializes the SD card
// Sets the global flag `sdNotDetected` to true if the SD card cannot be initialized
void initializeSD()
{
#ifdef ENV_WROOM
    digitalWrite(LORA_CS_PIN, HIGH);
    digitalWrite(SD_CS_PIN, LOW);
    if (!SD.begin(SD_CS_PIN))
    {
        handleSDError();
    }
    else
    {
        Serial.println(STA_SD_DETECTED_OK);
    }
    digitalWrite(SD_CS_PIN, HIGH);
#elif defined(ENV_TTGO)
    SDSPI.begin(SD_SCK_PIN, SD_MISO_PIN, SD_MOSI_PIN, SD_CS_PIN);
    if (!SD.begin(SD_CS_PIN, SDSPI))
    {
        handleSDError();
    }
    else
    {
        Serial.println(STA_SD_DETECTED_OK);
    }
    digitalWrite(SD_CS_PIN, HIGH);
#endif
}

#endif // src\AllModule\SD\SDFunctions.hpp