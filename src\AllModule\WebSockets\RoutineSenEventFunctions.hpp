// src\AllModule\WebSockets\RoutineSenEventFunctions.hpp
#ifndef ROUTINESENEVENTFUNCTIONS_HPP
#define ROUTINESENEVENTFUNCTIONS_HPP

#include "../../AllNode/Common/FileRecoveryFunctions.hpp"

void addSensorData(JsonArray &array, int id, const String &name, const String &unit, float value)
{
    JsonObject obj = array.add<JsonObject>();
    obj["id"] = id;
    obj["name"] = name;
    obj["unit"] = unit;
    obj["value"] = value;
}

JsonDocument readSensorFile(const char *filePath)
{
    JsonDocument doc;
    if (!safeReadJsonFile(filePath, doc))
    {
        Serial.print("Failed to read file with recovery: ");
        Serial.println(filePath);
    }
    return doc;
}

void sendSensorUpdates()
{
    Serial.println("Updating sensors data");

    JsonDocument doc;
    JsonArray data = doc["data"].to<JsonArray>();

    JsonDocument cardsDoc;
    if (safeReadJsonFile(CARDS_AVAILABLE_PATH, cardsDoc))
    {
            JsonArray cards = cardsDoc["ids"];
            for (JsonObject card : cards)
            {
                String cardType = card["type"];
                uint8_t sensorIdCard = card["id"];
                int separatorIndex = cardType.lastIndexOf(' ');
                if (separatorIndex == -1)
                {
                    Serial.println("Invalid card type format.");
                    continue;
                }
                uint8_t cardId = cardType.substring(separatorIndex + 1).toInt();
                String parameterName = cardType.substring(0, separatorIndex);

                String filePath = "/sensor_" + String(cardId) + "_" + parameterName + ".json";
                JsonDocument sensorData = readSensorFile(filePath.c_str());

                if (!sensorData.isNull())
                {
                    String name = sensorData["name"].as<String>();
                    String unit = sensorData["unit"].as<String>();
                    float value = sensorData["value"];
                    float offset = sensorData["offset"];

                    addSensorData(data, sensorIdCard, name, unit, value + offset);
                }
            }
    }
    else
    {
        Serial.println("Failed to read cards file with recovery.");
    }

    doc["type"] = "update";

    String jsonString;
    serializeJson(doc, jsonString);

    Serial.println(jsonString);

    ws.textAll(jsonString);
    Serial.println("Execute routine sensors");
}

#endif // ROUTINESENEVENTFUNCTIONS_HPP