# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](http://semver.org/).

## [0.1.0] - 2024-07-01
### Added
- RGB handling functions for controlling an RGB LED:
  - `initializeRGB()` to initialize the RGB LED.
  - `setColor(Color color)` to change the color of the RGB LED.
  - `cycleColors()` to iterate through all the colors in the `Color` enumeration, changing the LED color every second.

## [0.2.0] - 2024-07-01

### Added
- `RGBFunctions.hpp` with functionalities to control an RGB LED, enabling the device to display different colors (WHITE, MAGENTA, YELLOW, CYAN) based on specific conditions or actions.
- `ButtonFunctions.hpp` introduces advanced button handling that allows the device to trigger different actions based on the duration of the button press. This includes setting the RGB LED to different colors depending on how long the button is pressed and executing specific actions when the button is released.

### Changed
- Enhanced button debounce logic in `ButtonFunctions.hpp` to improve the accuracy of press duration detection and prevent false triggers.

### Fixed
- Adjustments to RGB LED color display timing to synchronize with button press durations more accurately.

This update significantly enhances the device's interactive capabilities, offering users a more intuitive and responsive way to control its network connectivity and visual feedback through the RGB LED. The introduction of time-sensitive button actions opens up new possibilities for user interaction and device functionality.

## [0.3.0] - 2024-07-01
### Added
- SD card handling functionality for detected.
- LoRa module handling functionality for detected.

### Changed
- Improved error handling for SD card initialization.
- Improved error handling for LoRa module initialization.

## [0.4.0] - 2024-07-03

### Added
- `RS485Functions.hpp` with functionalities to handle RS485 communication, including initialization, CRC calculation, sending requests, and setting slave IDs.
- `LinkSensorFunctions.hpp` to manage sensor linking, including creating, updating, and deleting scan register files and parameter files, generating sensor registers, reading registered IDs, and resetting sensors. Using the configuration button by setting it to MAGENTA it is possible to link a sensor to the device and by setting it to CYAN it is possible to reset the device to the default values.
- `RoutineSensorFunctions.hpp` for scheduled sensor routines, including sending ID requests, reading sensor data with configurable factors, updating parameter values, and handling routine callbacks.

### Changed
- Enhanced `updateScanRegisterFile()` in `LinkSensorFunctions.hpp` to include parameter names in the scan register.
- Improved `readSensorData()` in `RoutineSensorFunctions.hpp` to utilize the factor from the JSON parameter file for accurate value calculation.

### Fixed
- Corrected file handling logic in `RoutineSensorFunctions.hpp` to ensure parameter files are properly read and updated during routine callbacks.

This update significantly improves the RS485 handling capabilities and sensor management, providing robust functionalities for creating, updating, and deleting sensor-related files. The scheduled sensor routines are now more accurate and configurable, ensuring precise sensor data readings and updates.

### CHANGELOG

## [0.5.0] - 2024-07-03

### Added
- `LoRaMessageConfig.hpp` with task configuration for sending LoRa messages every 30 seconds.
- `LoRaMessageFunctions.hpp` with functions to read the scan register, check sensor states, read sensor data, and send LoRa messages with sensor information.

This update introduces the capability to send periodic LoRa messages containing sensor state and data, enhancing the system's ability to communicate sensor information wirelessly.

## [0.6.0] - 2024-07-03

### Added
- `SleepConfig.hpp` with configuration for sleep mode, including constants and function prototypes.
- `SleepFunctions.hpp` with functions to manage the sleep mode, including entering deep sleep, checking sleep conditions, managing sleep mode, and setting sleep mode status.
- Interrupt handler `checkButtonPress()()` to toggle the sleep mode state when a button is pressed.

This update introduces sleep mode handling functionality, allowing the device to enter deep sleep mode based on a timer or a button configuration press, and wake up on a GPIO interrupt or timer. The functionality can be enabled or disabled dynamically, enhancing the device's power management capabilities.

## [0.7.0] - 2024-07-10

### Added
- `WiFiConfig.hpp` defines a struct for WiFi credentials and a constant for the credentials file path.
- `WiFiFunctions.hpp` includes functions to load WiFi credentials from a JSON file, connect and disconnect from WiFi, and manage the WiFi state.

### Changed
- Modified the `case Yellow` to ensure that the WebServer and WebSocket are only enabled if the WiFi connection is successful.

This update allows for dynamic WiFi configuration through a JSON file on the SD card, improving flexibility and ease of use for network setup. The system now ensures that dependent services like the WebServer and WebSocket are only started when WiFi is connected, enhancing the reliability of the network operations.

## [0.7.3] - 2024-07-10

### Added
- `WebServerConfig.hpp` defines constants for file paths to be served by the web server and initializes the server instance.
- `WebServerFunctions.hpp` includes functions to initialize, start, stop, and manage the web server state.

### Changed
- Integration with WiFi control to ensure the web server is only enabled if WiFi is connected successfully.

This update introduces a flexible and scalable way to manage the web server, allowing for dynamic control of its state based on WiFi connectivity. This ensures that the server is only running when needed, optimizing resource usage and improving system reliability.

## [0.7.9] - 2024-07-10

### Added
- `WebSocketsConfig.hpp` defines constants for the WebSocket server and initializes the server instance and related tasks.
- `WebSocketsFunctions.hpp` includes functions to handle WebSocket requests for sensor data and cards, send sensor updates, and manage WebSocket connections.

### Changed
- Integrated WebSocket management with the WiFi and WebServer control to ensure WebSocket server is only enabled if WiFi is connected and the WebServer is running.

This update introduces comprehensive WebSocket functionality, allowing for real-time updates and dynamic interaction with the connected clients. It enhances the flexibility and scalability of the system, providing a robust foundation for further expansion and integration.

## [0.8.0] - 2024-07-10

### Added
- `handleGetSystemInfo` function to handle requests for system information, reading from `system_info.json` and responding with system details.
- `handleGetCredentials` function to handle requests for WiFi credentials, reading from `credentials.json` and responding with the credentials.
- `handleUpdateCredentials` function to handle requests for updating WiFi credentials, writing the new credentials to `credentials.json` and responding with the update status.
- WebSocket event handling for `get_system_info`, `get_credentials`, and `update_credentials` requests.

### Changed
- Updated `onWsEvent` to include handling for `get_system_info`, `get_credentials`, and `update_credentials` requests.

This update enhances the WebSocket functionality by adding support for fetching and updating system information and WiFi credentials, allowing for more dynamic configuration management through the WebSocket interface.

## [0.9.0] - 2024-07-23

### Added

- **Deep Sleep Event Handling Functions**
  - `handleGetSleepConfig()`: Retrieves the sleep configuration settings from the SD card and responds with the current sleep configuration.
  - `handleUpdateSleepConfig()`: Updates the sleep configuration settings on the SD card based on the provided JSON data.

- **System Event Handling Functions**
  - `handleGetSystemInfo()`: Handles requests to get system information. Responds with the system info in JSON format.
  - `generateUUID()`: Generates a unique UUID for the device using the ESP32's MAC address.
  - `handleRegenerateSystemInfo()`: Handles requests to regenerate the system information. Updates the device ID and version in the system info file and responds with the updated information.

- **WiFi Handling Functions**
  - `connectWiFi()`: Connects to WiFi using the loaded credentials, with a connection timeout of 10 seconds. If the connection fails, sets up an Access Point (AP) mode.

- **Web Sockets Handling Functions**
  - `handleGetCredentials()`: Retrieves the WiFi credentials from the SD card and responds with the current credentials.
  - `handleUpdateCredentials()`: Updates the WiFi credentials on the SD card based on the provided JSON data and responds with the update status.

### Changed

- Enhanced `goToSleepModeDevice()` to configure deep sleep for minimizing the power consumption of the ESP32.
- Updated sleep mode configuration to be loaded from a JSON file on the SD card.
- Modified LoRa message handling to load the device ID from the `system_info.json` file.

### Fixed

- Corrected the formatting of the sensor data values to ensure they have one decimal place in the `sendSensorUpdates` function.
- Ensured proper removal of sensor parameters from the available sensors list when saving a new card.

### Documentation

- Added detailed documentation for the following functionalities:
  - Deep Sleep Event Handling Functions
  - System Event Handling Functions
  - WiFi Handling Functions
  - Web Sockets Handling Functions
  - LoRa Message Handling Functions

This update significantly improves the configurability and flexibility of the Gapy Node system, allowing for better power management, easier updates to WiFi credentials, and more robust system information handling.

## [0.9.7] - 2024-07-29

### Added
- **Updates Event Handling Functions**:
  - `handleOTAUpdate()`: Handles OTA update requests by parsing the incoming message, extracting the version and URL, and setting a flag to initiate the update.

- **Updates Handling Functions**:
  - `executeUpdate()`: Initiates the OTA update process by downloading the update from a specified URL and flashing it to the device, providing progress feedback.
  - `checkOTAUpdate()`: Checks if there is a pending OTA update and executes it if the flag is set.
  - `manageOTAUpdate()`: Manages the OTA update process by calling the `checkOTAUpdate()` function.
  - `handleUpdateUpload()`: Handles the OTA update upload via web server, writing the uploaded data to the device's flash memory and providing progress updates.
  - `initializeUpdateServer()`: Initializes the web server for handling OTA updates, setting up the required endpoints for receiving update files.
  - `cleanupTempFile()`: Removes a specified temporary file from the SD card.
  - `cleanupTempFolder()`: Cleans up the temporary folder by removing all files and the folder itself from the SD card.
  - `moveFileFromTemp()`: Moves a file from the temporary folder to the root directory on the SD card.
  - `handleFileUpload()`: Handles file uploads via the web server, writing the uploaded data to the SD card and providing progress updates.
  - `onRequest()`: Handles basic HTTP requests, responding with a 200 status code.
  - `onRequestUpload()`: Handles the HTTP POST request for file uploads, sending a success response upon completion.
  - `onUpload()`: Handles the actual data upload process for files, calling `handleFileUpload()` to manage the uploaded data.
  - `initalizeFileUploadServer()`: Initializes the web server for handling file uploads, setting up the required endpoints and CORS headers for file upload support.

### Changed
- Enhanced the overall functionality of the OTA update process to provide more detailed progress feedback and handle both manual and automatic updates.

### Fixed
- Improved error handling during the OTA update process to provide clearer feedback when failures occur.

This update significantly enhances the device's capability to handle over-the-air updates, providing a robust mechanism for updating firmware and managing file uploads.

## [0.9.8] - 2024-09-11

### Added
- **LoRaWAN Functions**:
  - Implemented `printHex2()` function to format and print byte values in hexadecimal.
  - Added `saveSession()` function to store LoRaWAN session data (keys, network ID, etc.) to SPIFFS for persistence across resets.
  - Implemented `restoreSession()` function to load a saved LoRaWAN session from SPIFFS, bypassing the need for rejoining the network.
  - Introduced `deleteSession()` function to remove a saved LoRaWAN session from SPIFFS.
  - Added `do_send()` function for preparing and sending LoRaWAN messages with sensor data.
  - Implemented the `onEvent()` function to handle various LoRaWAN events such as joining the network, completing transmissions, and handling received data.
  - Introduced `initalizeLoRaWAN()` function to initialize the LoRaWAN stack, check for saved sessions, and manage the network join process if needed.
  - Added `loopLoRaWAN()` function to manage the LMIC run loop for LoRaWAN message handling.

### Changed
- The LoRaWAN functionality now allows session persistence across resets, reducing the need for frequent rejoining of the network.
