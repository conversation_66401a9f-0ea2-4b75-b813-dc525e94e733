// src\AllModule\WebServer\WebServerFunctions.hpp
#ifndef WEBSERVERFUNCTIONS_HPP
#define WEBSERVERFUNCTIONS_HPP

void initializeWebServer()
{
    server.on("/", HTTP_GET, [](AsyncWebServerRequest *request)
              { request->send(SD, INDEX_HTML_PATH, "text/html"); });

    server.on("/vite.svg", HTTP_GET, [](AsyncWebServerRequest *request)
              { request->send(SD, VITE_SVG_PATH, "image/svg+xml"); });

    server.on("/index.js", HTTP_GET, [](AsyncWebServerRequest *request)
              { request->send(SD, INDEX_JS_PATH, "application/javascript"); });
    
    initializeUpdateServer();
    initalizeFileUploadServer();
}

void beginWebServer()
{
    server.begin();
    Serial.println("Web server started.");
}

void stopWebServer()
{
    server.end();
    Serial.println("Web server stopped.");
}

void handleWebServer()
{
    if (webServerEnabled)
    {
        initializeWebServer();
        beginWebServer();
    }
    else
    {
        stopWebServer();
    }
}

void setWebServerEnabled(bool enabled)
{
    webServerEnabled = enabled;
    handleWebServer();
}

#endif // WEBSERVERFUNCTIONS_HPP
