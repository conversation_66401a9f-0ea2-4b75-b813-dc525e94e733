// src\AllNode\Sleep\SleepFunctions.hpp
#ifndef SLEEPFUNCTIONS_HPP
#define SLEEPFUNCTIONS_HPP

#include "../Common/FileRecoveryFunctions.hpp"

bool loadSleepConfig(const char *path, SleepConfig &config)
{
    JsonDocument doc;
    if (!safeReadJsonFile(path, doc))
    {
        Serial.println("Failed to read sleep config file with recovery");
        return false;
    }

    config.timeToSleep = doc["timeSleep"];
    config.timeActiveNode = doc["timeActive"];
    config.sleepModeActiveFlag = doc["activeFlag"];

    return true;
}

void goToSleepModeDevice()
{
    Serial.println("Going to sleep now");

    // Configure deep sleep
    esp_sleep_pd_config(ESP_PD_DOMAIN_RTC_SLOW_MEM, ESP_PD_OPTION_OFF);
    esp_sleep_pd_config(ESP_PD_DOMAIN_RTC_FAST_MEM, ESP_PD_OPTION_OFF);

#ifdef ENV_WROOM
    esp_sleep_enable_ext0_wakeup((gpio_num_t)INTERRUPT_PIN, 0);
#elif defined(ENV_TTGO)
    esp_sleep_enable_ext0_wakeup(GPIO_NUM_25, 0);
#endif
    esp_sleep_enable_timer_wakeup(sleepConfig.timeToSleep * uS_TO_S_FACTOR);
    esp_deep_sleep_start();

    Serial.println("This will never be printed");
}

void timerBeforeSleep()
{
    unsigned long currentMillis = millis();

    if (currentMillis - previousMillis >= sleepConfig.timeActiveNode * 1000)
    {
        previousMillis = currentMillis;
        goToSleepFlag = true;
    }
}

void checkGoToSleepMode()
{
    if (sleepConfig.sleepModeActiveFlag)
    {
        timerBeforeSleep();
        if (goToSleepFlag)
        {
            goToSleepFlag = false;
            goToSleepModeDevice();
        }
    }
}

void manageSleepMode()
{
    checkGoToSleepMode();
}

void setSleepMode(bool enable)
{
    sleepConfig.sleepModeActiveFlag = enable;
    Serial.print("Sleep mode active: ");
    Serial.println(sleepConfig.sleepModeActiveFlag ? "Yes" : "No");
}

void initializeSleepConfig()
{
    if (!loadSleepConfig(SLEEP_CONFIG_PATH, sleepConfig))
    {
        Serial.println("Failed to load sleep config. Using default values.");
    }
    else
    {
        Serial.println("Sleep config loaded successfully.");
    }
}

#endif // SLEEPFUNCTIONS_HPP
