image: python:3.11

pipelines:
  branches:
    master:
      - step:
          name: Setup and Build Firmware for Production
          caches:
            - pip
          script:
            - python -m pip install --upgrade pip setuptools wheel
            - python3 -m pip install -U platformio azure-storage-blob
            - python scripts/generate_buildinfo.py > build_info.txt
            - LATEST_SEMVER=$(grep -oP '(?<=::set-output name=latest_semver::).*' build_info.txt)
            - platformio run -e ttgo-lora32-v21
            - mkdir -p artifacts
            - cp .pio/build/ttgo-lora32-v21/firmware.bin artifacts/prod_v${LATEST_SEMVER}.bin
            - python scripts/upload_to_azure.py artifacts/prod_v${LATEST_SEMVER}.bin
          artifacts:
            - artifacts/**
    release:
      - step:
          name: Setup and Build Firmware for Pre-Production
          caches:
            - pip
          script:
            - python -m pip install --upgrade pip setuptools wheel
            - python3 -m pip install -U platformio azure-storage-blob
            - python scripts/generate_buildinfo.py > build_info.txt
            - LATEST_SEMVER=$(grep -oP '(?<=::set-output name=latest_semver::).*' build_info.txt)
            - platformio run -e ttgo-lora32-v21
            - mkdir -p artifacts
            - cp .pio/build/ttgo-lora32-v21/firmware.bin artifacts/preprod_v${LATEST_SEMVER}.bin
            - python scripts/upload_to_azure.py artifacts/preprod_v${LATEST_SEMVER}.bin
          artifacts:
            - artifacts/**
    develop:
      - step:
          name: Setup and Build Firmware for Development
          caches:
            - pip
          script:
            - python -m pip install --upgrade pip setuptools wheel
            - python3 -m pip install -U platformio azure-storage-blob
            - python scripts/generate_buildinfo.py > build_info.txt
            - LATEST_SEMVER=$(grep -oP '(?<=::set-output name=latest_semver::).*' build_info.txt)
            - COMMIT_HASH=$(grep -oP '(?<=::set-output name=commit_hash::).*' build_info.txt)
            - platformio run -e ttgo-lora32-v21
            - mkdir -p artifacts
            - cp .pio/build/ttgo-lora32-v21/firmware.bin artifacts/dev_v${LATEST_SEMVER}_${COMMIT_HASH}.bin
            - python scripts/upload_to_azure.py artifacts/dev_v${LATEST_SEMVER}_${COMMIT_HASH}.bin
          artifacts:
            - artifacts/**
