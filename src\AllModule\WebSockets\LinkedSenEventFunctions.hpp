// src\AllModule\WebSockets\LinkedSenEventFunctions.hpp
#ifndef LINKEDSENEVENTFUNCTIONS_HPP
#define LINKEDSENEVENTFUNCTIONS_HPP

void handleGetSensorsRequest(AsyncWebSocketClient *client)
{
    File file = SD.open(SENSOR_AVAILABLE_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open sensor available file.");
        client->text("{\"type\":\"sensor_list\",\"sensors\":[]}");
        return;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to deserialize sensor available file.");
        client->text("{\"type\":\"sensor_list\",\"sensors\":[]}");
        return;
    }

    JsonArray ids = doc["ids"];
    if (!ids)
    {
        Serial.println("No sensors found in the file.");
        client->text("{\"type\":\"sensor_list\",\"sensors\":[]}");
        return;
    }

    JsonDocument responseDoc;
    responseDoc["type"] = "sensor_list";
    JsonArray sensors = responseDoc["sensors"].to<JsonArray>();

    for (JsonObject idEntry : ids)
    {
        JsonObject sensor = sensors.add<JsonObject>();
        sensor["id"] = idEntry["id"];
        sensor["parameters"] = idEntry["parameters"];
    }

    String jsonResponse;
    serializeJson(responseDoc, jsonResponse);
    client->text(jsonResponse);
}

#endif // LINKEDSENEVENTFUNCTIONS_HPP