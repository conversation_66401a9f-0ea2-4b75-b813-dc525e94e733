// src\AllModule\Updates\UpdatesFunctions.hpp
#ifndef UPDATESFUNCTIONS_HPP
#define UPDATESFUNCTIONS_HPP

void executeUpdate(const char *versionOTA, const char *url)
{
    Serial.println("Starting OTA Update...");

    if (WiFi.status() == WL_CONNECTED)
    {
        HTTPClient http;
        http.begin(url);
        int httpCode = http.GET();

        if (httpCode == HTTP_CODE_OK)
        {
            int len = http.getSize();
            WiFiClient *stream = http.getStreamPtr();

            if (Update.begin(len))
            {
                size_t written = 0;
                uint8_t buff[128] = {0};
                int lastProgress = 0;

                while (written < len)
                {
                    int bytes = stream->readBytes(buff, sizeof(buff));
                    if (bytes > 0)
                    {
                        Update.write(buff, bytes);
                        written += bytes;

                        int progress = (written * 100) / len;

                        if (progress >= lastProgress + 10)
                        {
                            lastProgress = progress;
                            String progressMessage = "{\"type\":\"ota_progress\",\"progress\":" + String(progress) + "}";
                            ws.textAll(progressMessage);
                        }
                    }
                }

                if (Update.end())
                {
                    Serial.println("Actualización completada.");
                    if (Update.isFinished())
                    {
                        Serial.println("Actualización exitosa, reiniciando...");
                        updatedFlag = false;
                        ESP.restart();
                    }
                    else
                    {
                        Serial.println("Actualización no completada.");
                        updatedFlag = false;
                    }
                }
                else
                {
                    Serial.println("Error #" + String(Update.getError()));
                    updatedFlag = false;
                }
            }
            else
            {
                Serial.println("No se pudo comenzar la actualización");
                updatedFlag = false;
            }
        }
        else
        {
            Serial.println("Error en la solicitud HTTP: " + String(httpCode));
            updatedFlag = false;
        }
        http.end();
    }
    else
    {
        Serial.println("No conectado al WiFi");
        updatedFlag = false;
    }
}

void checkOTAUpdate()
{
    if (updatedFlag)
    {
        executeUpdate(versionOTA, url);
        updatedFlag = false;
    }
}

void manageOTAUpdate()
{
    checkOTAUpdate();
}

void handleUpdateUpload(AsyncWebServerRequest *request, String filename, size_t index, uint8_t *data, size_t len, bool final)
{
    tSendSensorUpdates.disable();
    // tSendLoRaMessages.disable();
    tSensorRoutine.disable();

    static size_t totalSize = 0;
    static size_t written = 0;
    static int lastProgress = 0;

    if (!index)
    {
        Serial.printf("Update Start: %s\n", filename.c_str());
        totalSize = request->contentLength();
        written = 0;
        lastProgress = 0;
        if (!Update.begin(UPDATE_SIZE_UNKNOWN))
        {
            Update.printError(Serial);
        }
    }

    if (Update.write(data, len) != len)
    {
        Update.printError(Serial);
    }
    else
    {
        written += len;

        int progress = (written * 100) / totalSize;
        if (progress >= lastProgress + 10)
        {
            lastProgress = progress;
            String progressMessage = "{\"type\":\"ota_progress\",\"progress\":" + String(progress) + "}";
            ws.textAll(progressMessage);
        }
    }

    if (final)
    {
        if (Update.end(true))
        {
            Serial.printf("Update Success: %uB\n", index + len);
            String progressMessage = "{\"type\":\"ota_progress\",\"progress\":100}";
            ws.textAll(progressMessage);
        }
        else
        {
            Update.printError(Serial);
        }
    }
}

void initializeUpdateServer()
{
    server.on(
        "/update", HTTP_POST, [](AsyncWebServerRequest *request)
        {
    AsyncWebServerResponse *response = request->beginResponse(200, "text/plain", "Update Finished.");
    response->addHeader("Connection", "close");
    request->send(response);
    ESP.restart(); },
        handleUpdateUpload);
}

void cleanupTempFile(String filename)
{
    String tempPath = "/temp/" + filename;
    if (SD.exists(tempPath))
    {
        SD.remove(tempPath);
        Serial.printf("Removed temp file %s\n", tempPath.c_str());
    }
    else
    {
        Serial.printf("Temp file %s does not exist\n", tempPath.c_str());
    }
}

void cleanupTempFolder()
{
    File tempDir = SD.open("/temp");
    if (!tempDir)
    {
        Serial.println("Failed to open temp directory");
        return;
    }

    File file = tempDir.openNextFile();
    while (file)
    {
        String path = String(file.name());
        SD.remove(path);
        file.close();
        file = tempDir.openNextFile();
    }

    tempDir.close();
    SD.rmdir("/temp");
}

bool moveFileFromTemp(String filename)
{
    String tempPath = "/temp/" + filename;
    String newPath = "/" + filename;

    Serial.printf("Moving file %s to %s\n", tempPath.c_str(), newPath.c_str());

    File sourceFile = SD.open(tempPath, FILE_READ);
    if (!sourceFile)
    {
        Serial.printf("Failed to open source file %s\n", tempPath.c_str());
        return false;
    }

    File destFile = SD.open(newPath, FILE_WRITE);
    if (!destFile)
    {
        Serial.printf("Failed to open destination file %s\n", newPath.c_str());
        sourceFile.close();
        return false;
    }

    while (sourceFile.available())
    {
        uint8_t buffer[512];
        size_t bytesRead = sourceFile.read(buffer, sizeof(buffer));
        size_t bytesWritten = destFile.write(buffer, bytesRead);
        if (bytesRead != bytesWritten)
        {
            Serial.printf("Failed to write all bytes to destination file %s\n", newPath.c_str());
            sourceFile.close();
            destFile.close();
            return false;
        }
    }

    sourceFile.close();
    destFile.close();

    SD.remove(tempPath);

    return true;
}

void handleFileUpload(AsyncWebServerRequest *request, String filename, size_t index, uint8_t *data, size_t len, bool final)
{
    static File uploadFile;
    static size_t totalSize = 0;
    static size_t written = 0;
    static int lastProgress = 0;
    static bool uploadError = false;
    static String currentFilename;

    if (!index)
    {
        // Initialize new upload
        if (filename.startsWith("dist/"))
        {
            filename = filename.substring(5);
        }

        String path = "/temp/" + filename;
        int lastSlash = path.lastIndexOf('/');
        if (lastSlash > 0)
        {
            String dirPath = path.substring(0, lastSlash);
            SD.mkdir(dirPath.c_str());
        }

        Serial.printf("Upload Start: %s\n", filename.c_str());
        uploadFile = SD.open(path, FILE_WRITE);
        if (!uploadFile)
        {
            Serial.println("Failed to open file for writing");
            uploadError = true;
            return;
        }

        written = 0;
        lastProgress = 0;
        uploadError = false;
        currentFilename = filename;

        UploadedFile uf = {filename, request->contentLength(), false};
        uploadedFiles.push_back(uf);
    }

    if (uploadFile)
    {
        size_t bytesWritten = 0;
        size_t bytesToWrite = len;

        while (bytesToWrite > 0)
        {
            size_t writtenNow = uploadFile.write(data + bytesWritten, bytesToWrite);
            if (writtenNow > 0)
            {
                bytesWritten += writtenNow;
                bytesToWrite -= writtenNow;
                delay(1);
            }
            else
            {
                Serial.println("Failed to write all bytes to file");
                uploadError = true;
                break;
            }
        }

        written += bytesWritten;

        int progress = (written * 100) / request->contentLength();
        if (progress >= lastProgress + 10)
        {
            lastProgress = progress;
            String progressMessage = "{\"type\":\"file_upload_progress\",\"progress\":" + String(progress) + "}";
            ws.textAll(progressMessage);
        }

        if (final)
        {
            uploadFile.close();
            Serial.printf("Upload Success: %uB\n", index + len);
            String progressMessage = "{\"type\":\"file_upload_progress\",\"progress\":100}";
            ws.textAll(progressMessage);
            String successMessage = "{\"type\":\"file_upload_success\",\"message\":\"File " + filename + " uploaded successfully\"}";
            ws.textAll(successMessage);

            if (!uploadError)
            {
                Serial.printf("Moving file %s from temp to root\n", currentFilename.c_str());
                if (moveFileFromTemp(currentFilename))
                {
                    cleanupTempFile(currentFilename);
                    ws.textAll("{\"type\":\"file_upload_success\",\"message\":\"All files uploaded successfully\"}");
                }
                else
                {
                    ws.textAll("{\"type\":\"file_upload_error\",\"message\":\"Failed to move files from temp\"}");
                }
            }
            else
            {
                Serial.println("Failed uploaded " + filename);
            }

            totalSize = 0;
            written = 0;
            lastProgress = 0;
            uploadError = false;
            currentFilename = "";
            uploadedFiles.clear();
        }
    }
}

void onRequest(AsyncWebServerRequest *request)
{
    request->send(200);
}

void onRequestUpload(AsyncWebServerRequest *request)
{
    AsyncWebServerResponse *response = request->beginResponse(200, "text/plain", "File Uploaded Successfully");
    response->addHeader("Access-Control-Allow-Origin", "*");
    request->send(response);
    Serial.println("File Uploaded Successfully");
}

void onUpload(AsyncWebServerRequest *request, String filename, size_t index, uint8_t *data, size_t len, bool final)
{
    tSendSensorUpdates.disable();
    // tSendLoRaMessages.disable();
    tSensorRoutine.disable();
    handleFileUpload(request, filename, index, data, len, final);
}

void initalizeFileUploadServer()
{
    server.on("/upload", HTTP_POST, onRequestUpload, onUpload);

    server.on("/upload", HTTP_OPTIONS, [](AsyncWebServerRequest *request)
              {
    AsyncWebServerResponse *response = request->beginResponse(200);
    response->addHeader("Access-Control-Allow-Origin", "*");
    response->addHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
    response->addHeader("Access-Control-Allow-Headers", "Content-Type");
    request->send(response); });
}

#endif // UPDATESFUNCTIONS_HPP