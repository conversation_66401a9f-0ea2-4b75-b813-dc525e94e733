// src\AllModule\Updates\UpdatesConfig.hpp
#ifndef UPDATESCONFIG_HPP
#define UPDATESCONFIG_HPP

#include <HTTPClient.h>
#include <Update.h>
#include <vector>
#include <map>

// Manual
struct UploadedFile {
  String filename;
  size_t size;
  bool completed;
};

std::vector<UploadedFile> uploadedFiles;
std::map<String, size_t> fileSizes;

// Automatic
bool updatedFlag = false;
const char* versionOTA;
const char* url;

#endif // UPDATESCONFIG_HPP