// src\AllNode\LoRaWAN\LoRaWANConfig.hpp
#include <lmic.h>
#include <hal/hal.h>
#include <SPI.h>
#include <FS.h>
#include <SPIFFS.h>

#ifdef ENV_WROOM
#define LORA_CS_PIN 5
#define LORA_RST_PIN 4
#define LORA_DIO0_PIN 25
#define LORA_DIO1_PIN 32
#define LORA_DIO2_PIN 33
#elif defined(ENV_TTGO)
#define LORA_SCK_PIN 5
#define LORA_MISO_PIN 19
#define LORA_MOSI_PIN 27
#define LORA_CS_PIN 18
#define LORA_RST_PIN 23
#define LORA_DIO0_PIN 26
#define LORA_DIO1_PIN 33
#define LORA_DIO2_PIN 32
SPIClass LoRaSPI(VSPI);
#endif

#define LORAWAN_CREDENTIALS_PATH "/lorawan_credentials.json"
#define LORAWAN_SESSION_PATH "/lorawan_session.json"
#define LORAWAN_CONFIG_PATH "/lorawan_config.json"

u1_t lorawan<PERSON><PERSON>ui[8];
u1_t lorawanDeveui[8];
u1_t lorawanAppkey[16];

const int PAYLOAD_SIZE = 7;
uint8_t payload[PAYLOAD_SIZE];

static osjob_t sendjob;

const lmic_pinmap lmic_pins = {
    .nss = LORA_CS_PIN,
    .rxtx = LMIC_UNUSED_PIN,
    .rst = LORA_RST_PIN,
    .dio = {LORA_DIO0_PIN, LORA_DIO1_PIN, LORA_DIO2_PIN},
};

#define PAYLOAD_MAX_SIZE 50

#define PAYLOAD_MAX_LENGTH 64

uint8_t receivedPayload[PAYLOAD_MAX_LENGTH];
uint8_t receivedPayloadLength = 0;
bool commandReceivedFlag = false;

#define ID_COMMAND 0x01

struct LoRaWANConfig
{
    unsigned TX_INTERVAL;
    unsigned DELAY_LORAWAN;
};

LoRaWANConfig loRaWANConfig = {180, 15};