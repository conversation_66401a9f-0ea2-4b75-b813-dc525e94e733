# Gapy Node

This project is a monitoring system based on the ESP32, designed to obtain data from sensors through the RS485 bus and transmit it via LoRa. The system integrates environmental and soil temperature and humidity sensors, soil nutrient sensors, as well as air quality sensors.

The system is configured to transmit data for periods of time and enter a low power mode. The data collected from the sensors is stored in CSV files on an SD card for further analysis.

#### RGB Handling Functions

The project defines the functionalities necessary to control an RGB LED connected to an ESP32-C3-MINI-DK board. It provides an interface to initialize the RGB LED and to change its color based on predefined color enumerations.

| Function                | Description                                                                                                                                                                                                                                                                                                                   |
| ----------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `initializeRGB()`       | Initializes the RGB LED by setting up the GPIO pins as outputs and initializing the LED to off (BLACK). This function uses the `Adafruit_NeoPixel` library to begin the LED strip and set its initial state.                                                                                                                  |
| `setColor(Color color)` | Changes the color of the RGB LED to the specified color. The function accepts an enumeration that represents various colors such as BLACK, RED, GREEN, BLUE, MAGENTA, YELLOW, CYAN, and WHITE. This is achieved by setting the pixel color using the `pixels.setPixelColor` method and updating the LED with `pixels.show()`. |
| `cycleColors()`         | Iterates through all the colors defined in the `Color` enumeration, changing the RGB LED color every second. This function is useful for verifying that the LED can display the full range of colors and for providing a visual indication of the system's functionality.                                                     |

##### Enum Definition

The `Color` enum defines the colors that the RGB LED can display:

```cpp
enum Color
{
    BLACK,
    RED,
    GREEN,
    BLUE,
    MAGENTA,
    YELLOW,
    CYAN,
    WHITE
};
```

##### Example Usage

Below is an example of how to use the initializeRGB, setColor, and cycleColors functions in your main program:

```cpp
#include "RGBFunctions.hpp"

void setup() {
  Serial.begin(115200);
  initializeRGB();
}

void loop() {
  cycleColors();
}
```

In this example, the setup function initializes the RGB LED, and the loop function continuously calls cycleColors to cycle through the colors.

##### Purpose

The RGB handling functions serve several purposes:

1. Initialization: initializeRGB sets up the RGB LED for use, ensuring it is ready to display colors.
2. Color Control: setColor allows the LED to display any color from the predefined Color enum, providing flexibility in visual output.
3. Verification: cycleColors can be used to verify that each color channel of the RGB LED is functioning correctly and to provide a visual indication of system status.

These functions collectively ensure that the RGB LED can be easily controlled and tested, making it a useful component for various projects involving visual feedback.

#### Button Handling Functions

This section describes the functionality for handling button interactions, allowing the device to trigger specific actions based on how long the button is pressed. The button logic supports multiple time-sensitive actions, offering a dynamic way to interact with the device.

| Function                                       | Description                                                                                                                                                                                                                                                                                                      |
| ---------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `initializeButton()`                           | Initializes the button pin as an input with a pull-up resistor, preparing it for interaction.                                                                                                                                                                                                                    |
| `printColorState(ButtonColorState colorState)` | Prints the color state to the Serial monitor as the button is pressed, providing real-time feedback on the interaction.                                                                                                                                                                                          |
| `checkButtonPress()`                           | Monitors and debounces the button press, changing the RGB LED color according to the press duration. After releasing the button, it triggers specific actions based on the last color state: WHITE for a short press, MAGENTA for a 2-second press, YELLOW for a 5-second press, and CYAN for an 8-second press. |

This modular approach to button handling allows for easy expansion or modification of the button's behavior and associated actions within the device's functionality.

#### SD Handling Functions

| Function          | Description                                                                                     |
| ----------------- | ----------------------------------------------------------------------------------------------- |
| `initializeSD()`  | Initializes the SD card. Sets `sdNotDetected` to true if the SD card cannot be initialized.     |
| `handleSDError()` | Prints an error message to the Serial monitor if an error occurs during SD card initialization. |

#### LoRa Handling Functions

| Function            | Description                                                                         |
| ------------------- | ----------------------------------------------------------------------------------- |
| `initializeLoRa()`  | Initializes the LoRa module. Sets 915MHz, RST, CS and DIO0 to configuration module. |
| `sendLoRaMessage()` | Sends a LoRa message with the given content.                                        |

#### RS485 Handling Functions

| Function               | Description                                              |
| ---------------------- | -------------------------------------------------------- |
| `initializeRS485()`    | Initializes the RS485 module with the defined baud rate. |
| `calculateCRC()`       | Calculates the CRC16 Modbus for the provided data.       |
| `sendSlaveIDRequest()` | Sends a request to a slave device to read its ID.        |
| `setSlaveID()`         | Sets a new ID for a slave device.                        |

#### Link Sensor Handling Functions

| Function                    | Description                                                                                           |
| --------------------------- | ----------------------------------------------------------------------------------------------------- |
| `createScanRegisterFile()`  | Creates the scan register file with an empty JSON object.                                             |
| `updateScanRegisterFile()`  | Updates the scan register file with a new sensor ID and its parameters.                               |
| `deleteScanRegisterFile()`  | Deletes the scan register file.                                                                       |
| `createParameterFile()`     | Creates a parameter file with an empty JSON object.                                                   |
| `updateParameterFile()`     | Updates the parameter file with provided JSON data.                                                   |
| `deleteParameterFile()`     | Deletes a specified parameter file.                                                                   |
| `generateRegistersSensor()` | Generates parameter files for a sensor based on the sensor database.                                  |
| `getTotalSensors()`         | Returns the total number of sensors from the sensor database file.                                    |
| `readRegisteredIDs()`       | Reads and returns a vector of registered sensor IDs from the scan register file.                      |
| `generateNewID()`           | Generates a new sensor ID by finding the next available ID.                                           |
| `deleteParameterFiles()`    | Deletes all parameter files for a specified sensor ID.                                                |
| `clearScanRegisterFile()`   | Clears the scan register file, removing all registered sensor IDs.                                    |
| `resetAllSensors()`         | Resets all sensors by deleting their parameter files and clearing the scan register file.             |
| `scanAndRegisterSensors()`  | Scans for sensors, registers new sensors, updates existing sensor IDs, and generates parameter files. |

#### Routine Sensor Handling Functions

| Function                      | Description                                                                                  |
| ----------------------------- | -------------------------------------------------------------------------------------------- |
| `sendSlaveIDRequestRoutine()` | Sends a request to a slave device to read its ID and checks for a valid response.            |
| `readSensorData()`            | Reads the sensor data from the specified parameter file and updates the value with a factor. |
| `updateParameterValue()`      | Updates the parameter value in the specified JSON file.                                      |
| `sensorRoutineCallback()`     | Callback function for the sensor routine, reads and updates sensor data periodically.        |

#### LoRa Message Handling Functions

| Function                     | Description                                                                                 |
| ---------------------------- | ------------------------------------------------------------------------------------------- |
| `readScanRegister()`         | Reads and deserializes the scan register file to get the list of registered sensors.        |
| `checkSensorState()`         | Checks the state of a sensor by sending a request and verifying the response.               |
| `readSensorData()`           | Reads and deserializes the JSON data of a specific sensor parameter file.                   |
| `sendLoRaMessagesCallback()` | Callback function for sending LoRa messages, including sensor state and data.               |
| `loadSystemInfo()`           | Loads system information (model, version, deviceId) from a JSON file stored on the SD card. |

#### Sleep Mode Handling Functions

| Function                    | Description                                                                                      |
| --------------------------- | ------------------------------------------------------------------------------------------------ |
| `goToSleepModeDevice()`     | Puts the device into deep sleep mode, configured to wake up on a GPIO interrupt or timer.        |
| `timerBeforeSleep()`        | Checks if the predefined active time has elapsed and sets the flag to go to sleep.               |
| `checkGoToSleepMode()`      | Verifies if the device should go to sleep based on the active time and sleep mode flag.          |
| `manageSleepMode()`         | General function to manage the sleep mode by calling `checkGoToSleepMode()`.                     |
| `setSleepMode(bool enable)` | Enables or disables the sleep mode based on the parameter.                                       |
| `loadSleepConfig()`         | Loads the sleep configuration from a JSON file stored on the SD card.                            |
| `initializeSleepConfig()`   | Initializes the sleep configuration, loading from file or using default values if loading fails. |

#### WiFi Handling Functions

| Function                | Description                                                                                                                                                                                  |
| ----------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `loadWiFiCredentials()` | Loads WiFi credentials from a JSON file located at the given path into the provided credentials struct.                                                                                      |
| `connectWiFi()`         | Connects to WiFi using the loaded credentials, with a connection timeout of 10 seconds. If the connection fails, it sets up the device as an access point with predefined SSID and password. |
| `disconnectWiFi()`      | Disconnects from the current WiFi network.                                                                                                                                                   |
| `handleWiFi()`          | Manages the WiFi connection based on the `wifiEnabled` flag, loading credentials and connecting/disconnecting as needed.                                                                     |
| `setWiFiEnabled()`      | Sets the WiFi enabled status and calls `handleWiFi()` to manage the connection accordingly.                                                                                                  |

#### Web Server Handling Functions

| Function                | Description                                                                                                       |
| ----------------------- | ----------------------------------------------------------------------------------------------------------------- |
| `initializeWebServer()` | Sets up the web server routes and handlers for serving HTML, SVG, and JavaScript files from the SD card. Upload-Update Firmware and Files.          |
| `beginWebServer()`      | Starts the web server and prints a message to the serial monitor.                                                 |
| `stopWebServer()`       | Stops the web server and prints a message to the serial monitor.                                                  |
| `handleWebServer()`     | Manages the web server state based on the `webServerEnabled` flag, initializing or stopping the server as needed. |
| `setWebServerEnabled()` | Sets the web server enabled status and calls `handleWebServer()` to manage the server accordingly.                |

#### Web Sockets Handling Functions

| Function                 | Description                                                                                                                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------ |
| `onWsEvent()`            | Handles various WebSocket events such as connection, disconnection, and incoming messages. Routes requests to the appropriate handler functions. |
| `initializeWebSockets()` | Initializes the WebSocket server and sets up the event handler.                                                                                  |
| `loopWebSockets()`       | Cleans up WebSocket clients.                                                                                                                     |
| `handleWebSocket()`      | Manages the WebSocket server state based on the `webSocketEnabled` flag, initializing or stopping the server and related tasks as needed.        |
| `setWebSocketEnabled()`  | Sets the WebSocket server enabled status and calls `handleWebSocket()` to manage the server accordingly.                                         |

##### Routine Sensors Event Handling Functions

| Function              | Description                                                                                                                                                                                  |
| --------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `addSensorData()`     | Adds sensor data to the JSON array with provided id, name, unit, and value.                                                                                                                  |
| `readSensorFile()`    | Reads a sensor file from the SD card and returns its JSON content.                                                                                                                           |
| `sendSensorUpdates()` | Sends sensor updates to all connected WebSocket clients. Reads the `cards.json` file to determine which sensors to send updates for and retrieves the data from the respective sensor files. |

##### Linked Sensors Event Handling Functions

| Function                    | Description                                                                                    |
| --------------------------- | ---------------------------------------------------------------------------------------------- |
| `handleGetSensorsRequest()` | Handles a WebSocket request to get the list of available sensors from `SENSOR_AVAILABLE_PATH`. |

##### Cards Event Handling Functions

| Function                   | Description                                                                                                                   |
| -------------------------- | ----------------------------------------------------------------------------------------------------------------------------- |
| `handleGetCardsRequest()`  | Handles a WebSocket request to get the list of saved cards from `CARDS_AVAILABLE_PATH`.                                       |
| `handleSaveCardRequest()`  | Handles a WebSocket request to save a new card, updating both the `cards.json` and `SENSOR_AVAILABLE_PATH` files accordingly. |
| `handleGetSensorDetails()` | Handles a WebSocket request to get detailed information about a specific sensor, reading from the respective sensor file.     |
| `handleUpdateOffset()`     | Handles a WebSocket request to update the offset value for a specific sensor, updating the respective sensor file.            |
| `handleDeleteCardRequest()`     | Handles a WebSocket request to delete a card, updating both the `cards.json` and `SENSOR_AVAILABLE_PATH` files accordingly, restoring the sensor parameter in `SENSOR_AVAILABLE_PATH`. |

##### Credentials Event Handling Functions

| Function                    | Description                                                                                                              |
| --------------------------- | ------------------------------------------------------------------------------------------------------------------------ |
| `handleGetCredentials()`    | Handles requests to get WiFi credentials. Responds with the credentials in JSON format.                                  |
| `handleUpdateCredentials()` | Handles requests to update WiFi credentials. Updates the credentials on the SD card and responds with the update status. |

##### Deep Sleep Event Handling Functions

| Function                    | Description                                                                                                           |
| --------------------------- | --------------------------------------------------------------------------------------------------------------------- |
| `handleGetSleepConfig()`    | Handles requests to get sleep configuration. Responds with the current sleep config in JSON format.                   |
| `handleUpdateSleepConfig()` | Handles requests to update sleep configuration. Updates the sleep config in JSON format and responds with the status. |

##### System Event Handling Functions

| Function                       | Description                                                                                                                                                        |
| ------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `handleGetSystemInfo()`        | Handles requests to get system information. Responds with the system info in JSON format.                                                                          |
| `generateUUID()`               | Generates a unique device ID using the MAC address of the ESP32.                                                                                                   |
| `handleRegenerateSystemInfo()` | Handles requests to regenerate system information. Updates the device ID and version in the system info file and responds with the new system info in JSON format. |

##### Updates Event Handling Functions

| Function                       | Description                                                                                                                                                        |
| ------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `handleOTAUpdate()`        | Handles Over-The-Air (OTA) update requests by deserializing the provided JSON message to extract the version and URL for the update, and sets a flag to indicate an update is required.  |

##### Unlinked Sensors Event Functions

| Function                       | Description                                                                                                                   |
| ------------------------------ | ----------------------------------------------------------------------------------------------------------------------------- |
| `handleGetRS485Id()`           | Handles a WebSocket request to retrieve the RS485 ID from a sensor.                                                            |
| `handleSetRS485Id()`           | Handles a WebSocket request to set a new RS485 ID for a sensor and confirm the ID change.                                       |
| `getRS485Id()`                 | Retrieves the RS485 ID by sending a request and reading the response from the sensor.                                           |
| `getSensorTypeFromRegister()`  | Retrieves the sensor type from the `scan_register.json` file based on the provided sensor ID.                                   |
| `handleGetSensorInfo()`        | Handles a WebSocket request to retrieve detailed information about a sensor using the RS485 ID and `scan_register.json`.        |
| `removeSensorFromCards()`      | Removes all cards associated with a sensor from the `cards.json` file.                                                         |
| `removeSensorFromAvailable()`  | Removes the sensor from the `sensors_availables.json` file.                                                                    |
| `removeSensorFromScanRegister()`| Removes the sensor from the `scan_register.json` file.                                                                         |
| `removeParameterFiles()`       | Deletes the sensor's parameter files from the SD card based on the sensor ID in `scan_register.json`.                           |
| `handleUnlinkSensor()`         | Handles a WebSocket request to unlink a sensor by removing it from the related JSON files and deleting its parameter files.     |

#### Updates Handling Functions

| Function                       | Description                                                                                                                                                        |
| ------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `executeUpdate()`              | Initiates the OTA update process by downloading the update from a specified URL and flashing it to the device, providing progress feedback.                          |
| `checkOTAUpdate()`             | Checks if there is a pending OTA update and executes it if the flag is set.                                                                                        |
| `manageOTAUpdate()`            | Manages the OTA update process by calling the `checkOTAUpdate()` function.                                                                                          |
| `handleUpdateUpload()`         | Handles the OTA update upload via web server, writing the uploaded data to the device's flash memory and providing progress updates.                               |
| `initializeUpdateServer()`     | Initializes the web server for handling OTA updates, setting up the required endpoints for receiving update files.                                                  |
| `cleanupTempFile()`            | Removes a specified temporary file from the SD card.                                                                                                               |
| `cleanupTempFolder()`          | Cleans up the temporary folder by removing all files and the folder itself from the SD card.                                                                        |
| `moveFileFromTemp()`           | Moves a file from the temporary folder to the root directory on the SD card.                                                                                       |
| `handleFileUpload()`           | Handles file uploads via the web server, writing the uploaded data to the SD card and providing progress updates.                                                   |
| `onRequest()`                  | Handles basic HTTP requests, responding with a 200 status code.                                                                                                    |
| `onRequestUpload()`            | Handles the HTTP POST request for file uploads, sending a success response upon completion.                                                                         |
| `onUpload()`                   | Handles the actual data upload process for files, calling `handleFileUpload()` to manage the uploaded data.                                                        |
| `initalizeFileUploadServer()`  | Initializes the web server for handling file uploads, setting up the required endpoints and CORS headers for file upload support.                                 |

#### LoRaWAN Handling Functions

| Function                   | Description                                                                                                                   |
| -------------------------- | ----------------------------------------------------------------------------------------------------------------------------- |
| `printHex2()`              | Prints a byte value in hexadecimal format, ensuring two digits are displayed.                                                  |
| `saveSession()`            | Saves the LoRaWAN session keys, network ID, and other session-related data to SPIFFS for persistent storage.                    |
| `restoreSession()`         | Restores the LoRaWAN session from SPIFFS if a session file exists.                                                             |
| `deleteSession()`          | Deletes the LoRaWAN session file from SPIFFS, if it exists.                                                                    |
| `do_send()`                | Prepares and sends a LoRaWAN message, including sensor data, using the LMIC library.                                            |
| `onEvent()`                | Handles LoRaWAN events, such as join, transmission complete, and received data, triggering appropriate actions like session saving. |
| `initalizeLoRaWAN()`       | Initializes the LoRaWAN stack, restores the session if available, and starts the join process if no session is found.           |
| `loopLoRaWAN()`            | Handles the LoRaWAN run loop, ensuring the LoRaWAN tasks are processed.                                                        |
