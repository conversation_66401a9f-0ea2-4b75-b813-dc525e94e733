// src\AllNode\LoRa\LoRaMessageFunctions.hpp
#ifndef LORAMESSAGEFUNCTIONS_HPP
#define LORAMESSAGEFUNCTIONS_HPP

JsonDocument readScanRegister()
{
    JsonDocument doc;
    File file = SD.open(SCAN_REGISTER_PATH, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (error)
        {
            Serial.println("Failed to deserialize scan_register file.");
        }
    }
    else
    {
        Serial.println("Failed to open scan_register file.");
    }
    return doc;
}

bool checkSensorState(uint8_t id, uint8_t readAddress, uint16_t idAddress)
{
    uint8_t request[8] = {id, readAddress, highByte(idAddress), lowByte(idAddress), 0x00, 0x01};
    uint16_t crc = calculateCRC(request, 6);
    request[6] = lowByte(crc);
    request[7] = highByte(crc);

    rs485Serial.write(request, 8);
    delay(100);

    if (rs485Serial.available())
    {
        uint8_t response[7];
        for (uint8_t i = 0; i < 7; i++)
        {
            response[i] = rs485Serial.read();
        }

        uint16_t crcResponse = calculateCRC(response, 5);
        return (response[0] == id && crcResponse == (response[6] << 8 | response[5]));
    }
    return false;
}

JsonDocument readSensorData(const String &filePath)
{
    JsonDocument doc;
    File file = SD.open(filePath, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(doc, file);
        file.close();
        if (error)
        {
            Serial.print("Failed to deserialize file: ");
            Serial.println(filePath);
        }
    }
    else
    {
        Serial.print("Failed to open file: ");
        Serial.println(filePath);
    }
    return doc;
}

bool loadSystemInfo(const char *path, SystemInfo &info)
{
    File file = SD.open(path, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open system info file");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse system info file");
        return false;
    }

    info.model = doc["model"].as<String>();
    info.version = doc["version"].as<String>();
    info.deviceId = doc["deviceId"].as<String>();

    return true;
}

// void sendLoRaMessagesCallback()
// {
//     Serial.println("Starting LoRa message sending task.");

//     JsonDocument scanRegister = readScanRegister();
//     JsonArray ids = scanRegister["ids"];

//     for (JsonObject idEntry : ids)
//     {
//         uint8_t idSensor = idEntry["id"];
//         bool state = checkSensorState(idSensor);

//         JsonDocument messageDoc;
//         messageDoc["idDevice"] = systemInfo.deviceId;
//         messageDoc["idSensor"] = idSensor;
//         messageDoc["state"] = state ? "available" : "unavailable";
//         JsonArray parameters = messageDoc["parameters"].to<JsonArray>();

//         if (state)
//         {
//             JsonArray paramNames = idEntry["parameters"];
//             for (JsonVariant paramName : paramNames)
//             {
//                 String filePath = "/sensor_" + String(idSensor) + "_" + paramName.as<String>() + ".json";
//                 JsonDocument sensorData = readSensorData(filePath.c_str());

//                 JsonObject param = parameters.add<JsonObject>();
//                 param["name"] = paramName.as<String>();
//                 param["data"] = sensorData["value"].as<float>() + sensorData["offset"].as<float>();
//             }
//         }

//         String message;
//         serializeJson(messageDoc, message);
//         sendLoRaMessage(message);
//         Serial.print("Sent LoRa message: ");
//         Serial.println(message);
//     }

//     Serial.println("LoRa message sending task completed.");
//     sendMessageLoRaFlag = true;
// }

#endif // LORAMESSAGEFUNCTIONS_HPP

