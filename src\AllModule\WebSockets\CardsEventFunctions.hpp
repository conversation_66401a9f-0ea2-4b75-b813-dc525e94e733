// src\AllModule\WebSockets\CardsEventFunctions.hpp
#ifndef CARDSEVENTFUNCTIONS_HPP
#define CARDSEVENTFUNCTIONS_HPP

void handleGetCardsRequest(AsyncWebSocketClient *client)
{
    File file = SD.open(CARDS_AVAILABLE_PATH, FILE_READ);
    if (file)
    {
        String fileContent;
        while (file.available())
        {
            fileContent += char(file.read());
        }
        file.close();
        client->text(fileContent);
    }
    else
    {
        client->text("{\"ids\":[]}");
    }
}

void handleSaveCardRequest(AsyncWebSocketClient *client, const String &msg)
{
    JsonDocument doc;
    deserializeJson(doc, msg);
    JsonObject card = doc["card"];
    String cardName = card["name"];
    String cardType = doc["type"];

    int separatorIndex = cardName.lastIndexOf(' ');
    if (separatorIndex == -1)
    {
        Serial.println("Invalid card name format.");
        return;
    }
    uint8_t cardId = cardName.substring(separatorIndex + 1).toInt();
    String parameterName = cardName.substring(0, separatorIndex);

    JsonDocument cardsDoc;
    File file = SD.open(CARDS_AVAILABLE_PATH, FILE_READ);
    if (file)
    {
        deserializeJson(cardsDoc, file);
        file.close();
    }
    else
    {
        cardsDoc["ids"] = JsonArray();
    }

    JsonArray ids = cardsDoc["ids"].as<JsonArray>();
    ids.add(card);

    file = SD.open(CARDS_AVAILABLE_PATH, FILE_WRITE);
    if (file)
    {
        serializeJson(cardsDoc, file);
        file.close();
    }

    JsonDocument availableDoc;
    file = SD.open(SENSOR_AVAILABLE_PATH, FILE_READ);
    if (file)
    {
        deserializeJson(availableDoc, file);
        file.close();
    }
    else
    {
        Serial.println("Failed to open sensor available file.");
        return;
    }

    JsonArray availableIds = availableDoc["ids"];
    for (JsonObject idEntry : availableIds)
    {
        if (idEntry["id"] == cardId)
        {
            JsonArray parameters = idEntry["parameters"];
            for (size_t i = 0; i < parameters.size(); i++)
            {
                if (parameters[i] == parameterName)
                {
                    parameters.remove(i);
                    break;
                }
            }
            break;
        }
    }

    file = SD.open(SENSOR_AVAILABLE_PATH, FILE_WRITE);
    if (file)
    {
        serializeJson(availableDoc, file);
        file.close();
    }

    String jsonString;
    serializeJson(card, jsonString);
    client->text("{\"type\":\"save_card\", \"card\":" + jsonString + "}");
}

void handleGetSensorDetails(AsyncWebSocketClient *client, int sensorId)
{
    JsonDocument cardsDoc;
    File file = SD.open(CARDS_AVAILABLE_PATH, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(cardsDoc, file);
        file.close();
        if (!error)
        {
            JsonArray cards = cardsDoc["ids"];
            for (JsonObject card : cards)
            {
                if (card["id"] == sensorId)
                {
                    String cardType = card["type"];
                    String name = card["name"];
                    int separatorIndex = cardType.lastIndexOf(' ');
                    if (separatorIndex == -1)
                    {
                        Serial.println("Invalid card type format.");
                        return;
                    }
                    uint8_t cardId = cardType.substring(separatorIndex + 1).toInt();
                    String parameterName = cardType.substring(0, separatorIndex);
                    String filePath = "/sensor_" + String(cardId) + "_" + parameterName + ".json";
                    JsonDocument sensorData = readSensorFile(filePath.c_str());

                    if (!sensorData.isNull())
                    {
                        String unit = sensorData["unit"];
                        float offset = sensorData["offset"];
                        String sign = offset >= 0 ? "+" : "-";
                        offset = fabs(offset);

                        JsonDocument responseDoc;
                        responseDoc["type"] = "sensor_details";
                        responseDoc["id"] = sensorId;
                        responseDoc["name"] = name;
                        responseDoc["sensorType"] = cardType;
                        responseDoc["unit"] = unit;
                        responseDoc["sign"] = sign;
                        responseDoc["offset"] = offset;

                        String jsonResponse;
                        serializeJson(responseDoc, jsonResponse);
                        client->text(jsonResponse);

                        return;
                    }
                }
            }
        }
        else
        {
            Serial.println("Failed to deserialize cards file.");
        }
    }
    else
    {
        Serial.println("Failed to open cards file.");
    }
}

void handleUpdateOffset(AsyncWebSocketClient *client, int sensorId, float newOffset)
{
    JsonDocument cardsDoc;
    File file = SD.open(CARDS_AVAILABLE_PATH, FILE_READ);
    if (file)
    {
        DeserializationError error = deserializeJson(cardsDoc, file);
        file.close();
        if (!error)
        {
            JsonArray cards = cardsDoc["ids"];
            for (JsonObject card : cards)
            {
                if (card["id"] == sensorId)
                {
                    String cardType = card["type"];
                    int separatorIndex = cardType.lastIndexOf(' ');
                    if (separatorIndex == -1)
                    {
                        Serial.println("Invalid card type format.");
                        return;
                    }
                    uint8_t cardId = cardType.substring(separatorIndex + 1).toInt();
                    String parameterName = cardType.substring(0, separatorIndex);
                    String filePath = "/sensor_" + String(cardId) + "_" + parameterName + ".json";
                    JsonDocument sensorData = readSensorFile(filePath.c_str());

                    if (!sensorData.isNull())
                    {
                        sensorData["offset"] = newOffset;

                        file = SD.open(filePath.c_str(), FILE_WRITE);
                        if (file)
                        {
                            serializeJson(sensorData, file);
                            file.close();

                            JsonDocument responseDoc;
                            responseDoc["type"] = "offset_updated";
                            responseDoc["id"] = sensorId;
                            responseDoc["status"] = "success";

                            String jsonResponse;
                            serializeJson(responseDoc, jsonResponse);
                            client->text(jsonResponse);

                            return;
                        }
                        else
                        {
                            Serial.println("Failed to open sensor file for writing.");
                        }
                    }
                }
            }
        }
        else
        {
            Serial.println("Failed to deserialize cards file.");
        }
    }
    else
    {
        Serial.println("Failed to open cards file.");
    }

    JsonDocument responseDoc;
    responseDoc["type"] = "offset_updated";
    responseDoc["id"] = sensorId;
    responseDoc["status"] = "failed";

    String jsonResponse;
    serializeJson(responseDoc, jsonResponse);
    client->text(jsonResponse);
}

void handleDeleteCardRequest(AsyncWebSocketClient *client, int cardId)
{
    // Leer el archivo de cards.json
    JsonDocument cardsDoc;
    File file = SD.open(CARDS_AVAILABLE_PATH, FILE_READ);
    if (file)
    {
        deserializeJson(cardsDoc, file);
        file.close();
    }
    else
    {
        client->text("{\"type\":\"delete_card_error\", \"message\":\"Failed to open cards file\"}");
        return;
    }

    JsonArray cards = cardsDoc["ids"].as<JsonArray>();
    JsonObject cardToDelete;
    String cardType;
    for (size_t i = 0; i < cards.size(); i++)
    {
        JsonObject card = cards[i];
        if (card["id"] == cardId)
        {
            cardToDelete = card;
            cardType = card["type"].as<String>();
            cards.remove(i);
            break;
        }
    }

    // Guardar los cambios en cards.json
    file = SD.open(CARDS_AVAILABLE_PATH, FILE_WRITE);
    if (file)
    {
        serializeJson(cardsDoc, file);
        file.close();
    }
    else
    {
        client->text("{\"type\":\"delete_card_error\", \"message\":\"Failed to save cards file\"}");
        return;
    }

    // Extraer nameParameter e idSensor del tipo de card
    int separatorIndex = cardType.lastIndexOf(' ');
    String nameParameter = cardType.substring(0, separatorIndex);
    int idSensor = cardType.substring(separatorIndex + 1).toInt();

    // Actualizar el archivo sensors_availables.json
    JsonDocument availableDoc;
    file = SD.open(SENSOR_AVAILABLE_PATH, FILE_READ);
    if (file)
    {
        deserializeJson(availableDoc, file);
        file.close();
    }
    else
    {
        client->text("{\"type\":\"delete_card_error\", \"message\":\"Failed to open sensors file\"}");
        return;
    }

    JsonArray availableIds = availableDoc["ids"].as<JsonArray>();
    for (JsonObject sensor : availableIds)
    {
        if (sensor["id"] == idSensor)
        {
            sensor["parameters"].as<JsonArray>().add(nameParameter);
            break;
        }
    }

    file = SD.open(SENSOR_AVAILABLE_PATH, FILE_WRITE);
    if (file)
    {
        serializeJson(availableDoc, file);
        file.close();
    }
    else
    {
        client->text("{\"type\":\"delete_card_error\", \"message\":\"Failed to save sensors file\"}");
        return;
    }

    // Enviar confirmación de eliminación
    client->text("{\"type\":\"delete_card_success\", \"message\":\"Card deleted successfully\"}");
}

#endif // CARDSEVENTFUNCTIONS_HPP