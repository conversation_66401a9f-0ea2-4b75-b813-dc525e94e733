// src\AllModule\WebSockets\UnlinkedSenEventFunctions.hpp
#ifndef UNLINKEDSENEVENTFUNCTIONS_HPP
#define UNLINKEDSENEVENTFUNCTIONS_HPP

void handleGetRS485Id(AsyncWebSocketClient *client)
{
    uint8_t addrRegister = 0;
    sendSlaveIDRequest(BROADCAST_COMMAND_ID, 0x03, 0x07, 0xD0);
    delay(100);

    while (rs485Serial.available())
    {
        uint8_t response[7];
        for (uint8_t i = 0; i < 7; i++)
        {
            response[i] = rs485Serial.read();
        }

        Serial.print("Response for new ID: ");
        for (int i = 0; i < 7; i++)
        {
            Serial.print(response[i], HEX);
            Serial.print(" ");
        }
        Serial.println();

        uint16_t crc = calculateCRC(response, 5);
        if (crc == (response[6] << 8 | response[5]))
        {
            addrRegister = response[0];
        }
    }

    // Enviar el ID recuperado al cliente WebSocket
    if (addrRegister != 0)
    {
        JsonDocument responseDoc;
        responseDoc["type"] = "rs485_id";
        responseDoc["id"] = addrRegister;
        String jsonResponse;
        serializeJson(responseDoc, jsonResponse);
        Serial.println(jsonResponse);
        client->text(jsonResponse);
    }
    else
    {
        client->text("{\"type\":\"error\", \"message\":\"Failed to retrieve RS485 ID\"}");
    }
}

void handleSetRS485Id(AsyncWebSocketClient *client, uint8_t currentId, uint8_t newId)
{
    setSlaveID(currentId, newId);
    delay(100);

    bool idChanged = false;

    // Verificar si el ID fue cambiado correctamente
    while (rs485Serial.available())
    {
        uint8_t response[8];
        for (uint8_t i = 0; i < 8; i++)
        {
            response[i] = rs485Serial.read();
        }

        Serial.print("Response for new ID: ");
        for (int i = 0; i < 8; i++)
        {
            Serial.print(response[i], HEX);
            Serial.print(" ");
        }
        Serial.println();

        uint16_t crc = calculateCRC(response, 6);
        if (crc == (response[7] << 8 | response[6]))
        {
            Serial.println("ID successfully changed.");
            idChanged = true;
        }
    }

    if (idChanged)
    {
        // Confirmar el nuevo ID
        sendSlaveIDRequest(newId, 0x03, 0x07, 0xD0);
        delay(100);

        while (rs485Serial.available())
        {
            uint8_t response[7];
            for (uint8_t i = 0; i < 7; i++)
            {
                response[i] = rs485Serial.read();
            }

            uint16_t crc = calculateCRC(response, 5);
            if (response[0] == newId && crc == (response[6] << 8 | response[5]))
            {
                Serial.println("ID register added");
                JsonDocument responseDoc;
                responseDoc["type"] = "set_rs485_id_success";
                responseDoc["newId"] = newId;
                String jsonResponse;
                serializeJson(responseDoc, jsonResponse);
                Serial.println(jsonResponse);
                client->text(jsonResponse);
                return;
            }
        }
    }

    // Enviar error si no se pudo cambiar el ID
    client->text("{\"type\":\"error\", \"message\":\"Failed to change RS485 ID\"}");
}

uint8_t getRS485Id()
{
    uint8_t addrRegister = 0;
    sendSlaveIDRequest(BROADCAST_COMMAND_ID, 0x03, 0x07, 0xD0);
    delay(100);

    while (rs485Serial.available())
    {
        uint8_t response[7];
        for (uint8_t i = 0; i < 7; i++)
        {
            response[i] = rs485Serial.read();
        }

        Serial.print("Response for new ID: ");
        for (int i = 0; i < 7; i++)
        {
            Serial.print(response[i], HEX);
            Serial.print(" ");
        }
        Serial.println();

        uint16_t crc = calculateCRC(response, 5);
        if (crc == (response[6] << 8 | response[5]))
        {
            addrRegister = response[0];
        }
    }

    return addrRegister;
}

String getSensorTypeFromRegister(uint8_t sensorId)
{
    // Abrir el archivo scan_register.json
    File file = SD.open(SCAN_REGISTER_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open scan_register.json");
        return "";
    }

    // Leer el archivo JSON
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse scan_register.json");
        return "";
    }

    // Buscar el sensorId en el JSON
    JsonArray ids = doc["ids"];
    for (JsonObject idEntry : ids)
    {
        if (idEntry["id"] == sensorId)
        {
            return idEntry["type"].as<String>();
        }
    }

    return "";
}

void handleGetSensorInfo(AsyncWebSocketClient *client)
{
    uint8_t addrRegister = getRS485Id();

    if (addrRegister == 0)
    {
        client->text("{\"type\":\"error\", \"message\":\"Failed to retrieve RS485 ID\"}");
        return;
    }

    // Buscar el tipo de sensor en scan_register.json
    String sensorType = getSensorTypeFromRegister(addrRegister);

    if (sensorType == "")
    {
        client->text("{\"type\":\"error\", \"message\":\"Sensor type not found\"}");
        return;
    }

    // Construir la respuesta para el cliente
    JsonDocument responseDoc;
    responseDoc["type"] = "sensor_info";
    responseDoc["id"] = addrRegister;
    responseDoc["sensorType"] = sensorType;

    String jsonResponse;
    serializeJson(responseDoc, jsonResponse);
    client->text(jsonResponse);
}

bool removeSensorFromCards(uint8_t sensorId)
{
    // Abrir el archivo cards.json para lectura
    File file = SD.open(CARDS_AVAILABLE_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open cards.json");
        return false;
    }

    // Deserializar el archivo JSON
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse cards.json");
        return false;
    }

    // Obtener el array de ids
    JsonArray ids = doc["ids"];

    // Iterar el array y eliminar las Cards asociadas al sensorId
    for (size_t i = 0; i < ids.size(); i++)
    {
        // Verificar si el "type" contiene el sensorId en el formato "nameParameter idSensor"
        String type = ids[i]["type"].as<String>();

        // Extraer el idSensor del "type"
        int separatorIndex = type.lastIndexOf(' '); // Buscar el espacio entre el nameParameter y el idSensor
        if (separatorIndex != -1)
        {
            String extractedId = type.substring(separatorIndex + 1); // Obtener el idSensor del "type"
            if (extractedId.toInt() == sensorId)
            {
                // Eliminar el objeto JSON del array si el idSensor coincide con sensorId
                ids.remove(i);
                i--; // Ajustar el índice tras eliminar un elemento
            }
        }
    }

    // Guardar el archivo actualizado
    file = SD.open(CARDS_AVAILABLE_PATH, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open cards.json for writing");
        return false;
    }

    // Escribir el JSON actualizado en el archivo
    serializeJson(doc, file);
    file.close();

    Serial.println("Sensor removed successfully from cards.json");
    return true;
}

bool removeSensorFromAvailable(uint8_t sensorId)
{
    File file = SD.open(SENSOR_AVAILABLE_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open sensors_availables.json");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse sensors_availables.json");
        return false;
    }

    JsonArray ids = doc["ids"];
    for (size_t i = 0; i < ids.size(); i++)
    {
        if (ids[i]["id"] == sensorId)
        {
            ids.remove(i);
            break;
        }
    }

    // Guardar el archivo actualizado
    file = SD.open(SENSOR_AVAILABLE_PATH, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open sensors_availables.json for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();
    return true;
}

bool removeSensorFromScanRegister(uint8_t sensorId)
{
    File file = SD.open(SCAN_REGISTER_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open scan_register.json");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse scan_register.json");
        return false;
    }

    JsonArray ids = doc["ids"];
    for (size_t i = 0; i < ids.size(); i++)
    {
        if (ids[i]["id"] == sensorId)
        {
            ids.remove(i);
            break;
        }
    }

    // Guardar el archivo actualizado
    file = SD.open(SCAN_REGISTER_PATH, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open scan_register.json for writing");
        return false;
    }

    serializeJson(doc, file);
    file.close();
    return true;
}

bool removeParameterFiles(uint8_t slaveAddress)
{
    // Abrir el archivo scan_register.json
    File file = SD.open(SCAN_REGISTER_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open scan_register.json");
        return false;
    }

    // Deserializar el contenido del archivo
    JsonDocument scanRegister;
    DeserializationError error = deserializeJson(scanRegister, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse scan_register.json");
        return false;
    }

    // Buscar el ID correspondiente en el array ids
    JsonArray ids = scanRegister["ids"];
    for (JsonObject idEntry : ids)
    {
        if (idEntry["id"] == slaveAddress)
        {
            // Obtener los parámetros asociados con el sensor
            JsonArray parameters = idEntry["parameters"];
            for (JsonVariant parameter : parameters)
            {
                String filePath = "/sensor_" + String(slaveAddress) + "_" + parameter.as<String>() + ".json";

                // Verificar si el archivo existe y eliminarlo
                if (SD.exists(filePath.c_str()))
                {
                    if (SD.remove(filePath.c_str()))
                    {
                        Serial.print("Deleted file: ");
                        Serial.println(filePath);
                    }
                    else
                    {
                        Serial.print("Failed to delete file: ");
                        Serial.println(filePath);
                        return false; // Si falla la eliminación de un archivo, retornar false
                    }
                }
                else
                {
                    Serial.print("File not found: ");
                    Serial.println(filePath);
                }
            }
            return true; // Operación exitosa si se eliminaron todos los archivos
        }
    }

    Serial.println("Slave address not found in scan_register.json");
    return false; // Retornar false si el ID no se encontró
}

void handleUnlinkSensor(AsyncWebSocketClient *client, uint8_t sensorId)
{
    // Eliminar el sensor de cards.json
    bool cardRemoved = removeSensorFromCards(sensorId);

    // Eliminar el sensor de sensors_availables.json
    bool sensorAvailableRemoved = removeSensorFromAvailable(sensorId);

    // Eliminar los archivos de parámetros del sensor
    bool parameterFilesRemoved = removeParameterFiles(sensorId);

    // Eliminar el sensor de scan_register.json
    bool scanRegisterRemoved = removeSensorFromScanRegister(sensorId);

    // Verificar si todas las operaciones fueron exitosas
    if (cardRemoved && sensorAvailableRemoved && scanRegisterRemoved && parameterFilesRemoved)
    {
        client->text("{\"type\":\"unlink_success\", \"message\":\"Sensor unlinked successfully\"}");
    }
    else
    {
        client->text("{\"type\":\"error\", \"message\":\"Failed to unlink sensor\"}");
    }
}

#endif // UNLINKEDSENEVENTFUNCTIONS_HPP