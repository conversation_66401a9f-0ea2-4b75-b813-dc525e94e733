// src\AllModule\WebSockets\SleepEventFunctions.hpp
#ifndef SLEEPEVENTFUNCTIONS_HPP
#define SLEEPEVENTFUNCTIONS_HPP

void handleGetSleepConfig(AsyncWebSocketClient *client)
{
    File file = SD.open(SLEEP_CONFIG_PATH, FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open sleep config file.");
        client->text("{\"type\":\"sleep_config\",\"timeToSleep\":\"30\",\"timeToActive\":\"30\",\"activeSleepMode\":true}");
        return;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error)
    {
        Serial.println("Failed to parse sleep config file.");
        client->text("{\"type\":\"sleep_config\",\"timeToSleep\":\"30\",\"timeToActive\":\"30\",\"activeSleepMode\":true}");
        return;
    }

    JsonDocument responseDoc;
    responseDoc["type"] = "sleep_config";
    responseDoc["timeToSleep"] = doc["timeSleep"].as<int>();
    responseDoc["timeToActive"] = doc["timeActive"].as<int>();
    responseDoc["activeSleepMode"] = doc["activeFlag"].as<bool>();

    String jsonResponse;
    serializeJson(responseDoc, jsonResponse);
    Serial.println(jsonResponse);
    client->text(jsonResponse);
}

void handleUpdateSleepConfig(AsyncWebSocketClient *client, const String &msg)
{
    JsonDocument doc;
    deserializeJson(doc, msg);

    int timeToSleep = doc["timeToSleep"];
    int timeToActive = doc["timeToActive"];
    bool activeSleepMode = doc["activeSleepMode"];

    JsonDocument newConfig;
    newConfig["timeSleep"] = timeToSleep;
    newConfig["timeActive"] = timeToActive;
    newConfig["activeFlag"] = activeSleepMode;

    File file = SD.open(SLEEP_CONFIG_PATH, FILE_WRITE);
    if (!file)
    {
        Serial.println("Failed to open sleep config file for writing.");
        client->text("{\"type\":\"update_status\",\"status\":\"failed\"}");
        return;
    }

    serializeJson(newConfig, file);
    file.close();

    client->text("{\"type\":\"update_status\",\"status\":\"success\"}");
}

#endif // SLEEPEVENTFUNCTIONS_HPP