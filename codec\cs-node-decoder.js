function decodeUplink(input) {
  var bytes = input.bytes;
  var data = { data: [] };
  var index = 0;

  // JSON de apoyo con la información de sensores
  var sensorInfo = {
    1: {
      name: "Soil Sensor THC-S",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "conductivity", factor: 1.0 },
      ],
    },
    2: {
      name: "Soil Sensor THCPH-S",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "conductivity", factor: 1.0 },
        { id: 4, name: "ph", factor: 10.0 },
      ],
    },
    3: {
      name: "Soil Sensor NPK-S",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "conductivity", factor: 1.0 },
        { id: 4, name: "ph", factor: 10.0 },
        { id: 5, name: "nitrogen", factor: 1.0 },
        { id: 6, name: "phosphorus", factor: 1.0 },
        { id: 7, name: "potassium", factor: 1.0 },
      ],
    },
    4: {
      name: "Env Sensor SAQ-TH",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
      ],
    },
    5: {
      name: "Env Sensor BY-TH",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
      ],
    },
    6: {
      name: "Env Sensor PS",
      parameters: [{ id: 1, name: "photosynthetic", factor: 1.0 }],
    },
    7: {
      name: "Env Sensor SWC-C",
      parameters: [{ id: 1, name: "windSpeed", factor: 10.0 }],
    },
    8: {
      name: "Env Sensor RG",
      parameters: [{ id: 1, name: "rainGauge", factor: 10.0 }],
    },
    9: {
      name: "Env Sensor BY-THCO2",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "co2", factor: 1.0 },
      ],
    },
    10: {
      name: "Env Sensor BY-THISCO2",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "humidity", factor: 10.0 },
        { id: 3, name: "co2", factor: 1.0 },
      ],
    },
    11: {
      name: "Env Sensor SAQ-CO2",
      parameters: [{ id: 1, name: "co2", factor: 1.0 }],
    },
    12: {
      name: "Env Sensor UWD",
      parameters: [
        { id: 1, name: "windSpeed", factor: 100.0 },
        { id: 2, name: "windDirection", factor: 1.0 },
        { id: 3, name: "temperature", factor: 10.0 },
        { id: 4, name: "humidity", factor: 10.0 },
        { id: 5, name: "pm2.5", factor: 1.0 },
        { id: 6, name: "pm10", factor: 1.0 },
        { id: 7, name: "noise", factor: 10.0 },
        { id: 8, name: "pressure", factor: 10.0 },
        { id: 9, name: "rainfall", factor: 10.0 },
      ],
    },
    13: {
      name: "Water Quality 5 in 1",
      parameters: [
        { id: 1, name: "temperature", factor: 100.0 },
        { id: 2, name: "ph", factor: 100.0 },
        { id: 3, name: "salinity", factor: 1000.0 },
        { id: 4, name: "conductivity", factor: 0.1 },
      ],
    },
    14: {
      name: "Water Level Pressure",
      parameters: [
        { id: 1, name: "highLevel", factor: 1.0 },
        { id: 2, name: "lowLevel", factor: 1.0 },
      ],
    },
    15: {
      name: "Water Level Ultrasonic",
      parameters: [{ id: 1, name: "distance", factor: 1.0 }],
    },
    16: {
      name: "Water PH",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "ph", factor: 10.0 },
      ],
    },
    17: {
      name: "Water EC",
      parameters: [
        { id: 1, name: "conductivity", factor: 1.0 },
      ],
    },
    18: {
      name: "Water PHEC",
      parameters: [
        { id: 1, name: "temperature", factor: 10.0 },
        { id: 2, name: "ph", factor: 100.0 },
        { id: 3, name: "conductivity", factor: 1.0 },
      ],
    },
  };

  //   while (index < bytes.length) {
  //     var idSensor = bytes[index++];
  //     var typeSensor = bytes[index++];
  //     var stateSensor = bytes[index++];

  //     var sensorData = {
  //       idSensor: idSensor,
  //       typeSensor: typeSensor,
  //       stateSensor: stateSensor === 1 ? "available" : "unavailable",
  //     };

  //     if (stateSensor === 1) {
  //       var parameters = sensorInfo[idSensor].parameters;
  //       for (var i = 0; i < parameters.length; i++) {
  //         var paramId = parameters[i].id;
  //         var lsb = bytes[index++];
  //         var msb = bytes[index++];
  //         var value = (lsb << 8) | msb;
  //         sensorData[parameters[i].name] = value / parameters[i].factor;
  //       }
  //     }

  //     data.data.push(sensorData);
  //   }

  //   return {
  //     data: data,
  //   };
  // Función para realizar la conversión IEEE 754 Float32
  function convertToFloat32(highBytes, lowBytes) {
    // Combinar los bytes en un único entero de 32 bits
    var raw =
      (highBytes[0] << 24) |
      (highBytes[1] << 16) |
      (lowBytes[0] << 8) |
      lowBytes[1];
    // Convertir a flotante usando DataView
    var buffer = new ArrayBuffer(4);
    new DataView(buffer).setUint32(0, raw);
    return new DataView(buffer).getFloat32(0);
  }

  while (index < bytes.length) {
    var idSensor = bytes[index++];
    var typeSensor = bytes[index++];
    var stateSensor = bytes[index++];

    var sensorData = {
      idSensor: idSensor,
      typeSensor: typeSensor,
      stateSensor: stateSensor === 1 ? "available" : "unavailable",
    };

    if (stateSensor === 1) {
      if (idSensor === 14) {
        // Extraer los bytes para highLevel y lowLevel
        var highBytes = [bytes[index++], bytes[index++]];
        var lowBytes = [bytes[index++], bytes[index++]];
        // Realizar la conversión a "distance"
        sensorData.distance = convertToFloat32(highBytes, lowBytes);
      } else {
        var parameters = sensorInfo[idSensor].parameters;
        for (var i = 0; i < parameters.length; i++) {
          var paramId = parameters[i].id;
          var lsb = bytes[index++];
          var msb = bytes[index++];
          var value = (lsb << 8) | msb;
          sensorData[parameters[i].name] = value / parameters[i].factor;
        }
      }
    }

    data.data.push(sensorData);
  }

  return {
    data: data,
  };
}

// Simular el payload recibido
// var input = {
//   bytes: [
//     0x02, 0x02, 0x01, 0x00, 0xcf, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x05,
//     0x05, 0x00, 0x01, 0x01, 0x01, 0x00, 0xc9, 0x00, 0x2f, 0x00, 0x00,
//   ],
// };

// Ejemplo de input para probar el sensor 14
var input = {
    bytes: [
      0x0e, 0x02, 0x01, // ID sensor, tipo, estado disponible
      0x41, 0xb4, // HighLevel bytes (parte alta del Float32)
      0x0a, 0xa6, // LowLevel bytes (parte baja del Float32)
    ],
  };

// Decodificar el payload
var result = decodeUplink(input);

// Imprimir el resultado
console.log(JSON.stringify(result, null, 2));
